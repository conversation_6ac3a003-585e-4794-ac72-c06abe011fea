body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    margin: 0;
    padding: 10px;
    background-color: #f0f2f5; 
    color: #050505;
    -webkit-tap-highlight-color: transparent; /* Убрать подсветку при тапе на мобильных */
    box-sizing: border-box; /* Для более предсказуемой работы с padding/border */
}

*, *:before, *:after {
    box-sizing: inherit;
}

.fb-style-comments-app-container {
    max-width: 600px; 
    margin: 0 auto;
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    padding: 15px;
}

/* --- Rating Section --- */
.fb-style-comments-rating-section {
    padding-bottom: 20px;
    margin-bottom: 20px;
    border-bottom: 1px solid #e0e0e0;
}

.fb-style-comments-rating-title {
    font-size: 1.1em;
    font-weight: 600;
    margin-bottom: 10px;
    color: #1c1e21;
}

.fb-style-comments-rating-container {
    /* Fallback для старых WebView без flexbox */
    display: table;
    width: 100%;
    margin-bottom: 15px;
    /* Современный flexbox для поддерживающих браузеров */
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
}

.fb-style-comments-stars .fb-style-comments-star {
    font-size: 1.8em;
    color: #bec3c9; /* Серые по умолчанию */
    cursor: pointer;
    padding: 8px; /* Увеличиваем touch target для мобильных */
    margin: 0 2px;
    transition: color 0.1s ease-in-out, transform 0.1s ease-in-out;
    /* Минимальный размер touch target 44px для мобильных */
    min-width: 44px;
    min-height: 44px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    /* Убираем подсветку при тапе на мобильных */
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
}

/* Ховер только если пользователь еще не выбрал */
.fb-style-comments-stars:not(.user-has-selected-rating) .fb-style-comments-star:hover, 
.fb-style-comments-stars .fb-style-comments-star.hovered { 
    color: #fadb14; /* Ярче желтый при наведении */
}

.fb-style-comments-stars .fb-style-comments-star.active {
    color: #FFD700; /* Gold - для выбранных */
}

.fb-style-comments-rating-stats {
    margin-left: 10px;
    font-size: 0.95em;
    color: #606770;
}

.fb-style-comments-rating-bars {
    font-size: 0.9em;
}

.fb-style-comments-rating-bar {
    /* Fallback для старых WebView */
    display: table;
    width: 100%;
    margin-bottom: 5px;
    color: #606770;
    /* Современный flexbox */
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
}

.fb-style-comments-bar-label {
    width: 30px;
    font-weight: 500;
    /* Fallback для старых WebView */
    display: table-cell;
    vertical-align: middle;
    /* Современный flexbox */
    -webkit-flex-shrink: 0;
    -ms-flex-negative: 0;
    flex-shrink: 0;
}

.fb-style-comments-bar-fill {
    /* Fallback для старых WebView */
    display: table-cell;
    width: 100%;
    vertical-align: middle;
    /* Современный flexbox */
    -webkit-box-flex: 1;
    -webkit-flex-grow: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    height: 8px;
    background-color: #e9ebee;
    border-radius: 4px;
    margin: 0 8px;
    overflow: hidden;
}

.fb-style-comments-bar-progress {
    height: 100%;
    background-color: #FFD700; 
    border-radius: 4px;
    transition: width 0.3s ease-in-out;
}

.fb-style-comments-bar-count {
    min-width: 30px;
    text-align: right;
    /* Fallback для старых WebView */
    display: table-cell;
    vertical-align: middle;
    /* Современный flexbox */
    -webkit-flex-shrink: 0;
    -ms-flex-negative: 0;
    flex-shrink: 0;
}

.fb-style-comments-user-rating-feedback {
    font-weight: 500;
}

/* --- Comment Form Section --- */
.fb-style-comments-comment-form-section {
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e0e0e0;
}
.fb-style-comments-form-title {
    font-size: 1.1em;
    font-weight: 600;
    margin-bottom: 15px;
    color: #1c1e21;
}
.fb-style-comments-form-group {
    margin-bottom: 10px;
}
.fb-style-comments-form-group input[type="text"],
.fb-style-comments-form-group textarea {
    width: 100%; 
    padding: 10px;
    border: 1px solid #ccd0d5;
    border-radius: 6px;
    font-size: 1em;
    background-color: #f5f6f7;
    -webkit-appearance: none; /* Убрать стандартные стили iOS */
    appearance: none;
    font-family: inherit; /* Наследование шрифта */
}
.fb-style-comments-form-group textarea {
    resize: vertical;
    min-height: 60px; /* Начальная высота */
    line-height: 1.4; /* Для лучшей читаемости */
    overflow-y: hidden; /* JS управляет высотой, чтобы избежать двойного скролла */
}
.fb-style-comments-submit-btn {
    background-color: #1877f2; 
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    font-size: 0.95em;
    transition: background-color 0.2s;
    -webkit-appearance: none;
    appearance: none;
    font-family: inherit;
    display: block; /* Для возможности установки margin: auto */
    margin-left: auto; /* Кнопка справа, если нужно, или width: 100% */
}
.fb-style-comments-submit-btn:hover,
.fb-style-comments-submit-btn:active {
    background-color: #166fe5;
}

.fb-style-comments-image-upload-btn {
    background-color: #42b883;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    font-size: 0.9em;
    transition: background-color 0.2s;
    -webkit-appearance: none;
    appearance: none;
    font-family: inherit;
    margin-bottom: 10px;
}

.fb-style-comments-image-upload-btn:hover,
.fb-style-comments-image-upload-btn:active {
    background-color: #369870;
}

/* --- Comments List --- */
.fb-style-comments-list-container {
    /* Стили для контейнера списка, если нужны */
}

.fb-style-comments-comment-item {
    margin-bottom: 15px;
    position: relative; 
}

.fb-style-comments-comment-main {
    /* Fallback для старых WebView */
    display: table;
    width: 100%;
    /* Современный flexbox */
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: start;
    -webkit-align-items: flex-start;
    -ms-flex-align: start;
    align-items: flex-start;
}

.fb-style-comments-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    margin-right: 10px;
    color: white;
    font-weight: 600;
    font-size: 0.9em;
    line-height: 36px;
    text-align: center;
    overflow: hidden; /* Если инициалы слишком длинные */
    /* Fallback для старых WebView */
    display: table-cell;
    vertical-align: top;
    /* Современный flexbox */
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-flex-shrink: 0;
    -ms-flex-negative: 0;
    flex-shrink: 0;
}
.fb-style-comments-company-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    /* Убираем object-fit для совместимости с старыми WebView */
    /* object-fit: cover; */
}


.fb-style-comments-comment-content {
    /* Fallback для старых WebView */
    display: table-cell;
    width: 100%;
    vertical-align: top;
    /* Современный flexbox */
    -webkit-box-flex: 1;
    -webkit-flex-grow: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    /* Заменяем calc() на фиксированное значение для совместимости */
    max-width: 85%; /* Примерно calc(100% - 46px) */
}

.fb-style-comments-comment-bubble {
    background-color: #f0f2f5; 
    padding: 8px 12px;
    border-radius: 18px; 
    margin-bottom: 5px;
    word-wrap: break-word; 
    overflow-wrap: break-word; 
}

.fb-style-comments-author-name {
    font-weight: 600;
    font-size: 0.9em;
    color: #050505;
    display: block; 
    margin-bottom: 2px;
}
.fb-style-comments-company-reply .fb-style-comments-author-name {
    color: #1877f2; 
}


.fb-style-comments-text {
    font-size: 0.95em;
    line-height: 1.35;
    margin: 0;
    color: #050505;
    white-space: pre-wrap; /* Сохраняет переносы строк из textarea */
}

.fb-style-comments-image-attachment {
    margin-top: 8px;
}
.fb-style-comments-image-attachment img {
    max-width: 100%;
    height: auto; 
    border-radius: 8px;
    display: block; 
}

.fb-style-comments-actions {
    font-size: 0.8em;
    color: #606770;
    padding-left: 12px;
    /* Fallback для старых WebView */
    display: block;
    /* Современный flexbox с префиксами */
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}

.fb-style-comments-actions > * {
    margin-right: 8px; /* Отступы между элементами */
}
.fb-style-comments-actions > *:last-child {
    margin-right: 0;
}

.fb-style-comments-actions button {
    background: none;
    border: none;
    color: #606770;
    font-weight: 600;
    cursor: pointer;
    /* Увеличиваем touch target для мобильных */
    padding: 8px 12px;
    margin: 0;
    font-size: 1em;
    font-family: inherit;
    vertical-align: middle;
    /* Минимальный размер для touch */
    min-height: 44px;
    border-radius: 4px;
    /* Убираем подсветку при тапе на мобильных */
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
    transition: opacity 0.15s ease-in-out;
}
.fb-style-comments-actions button:hover,
.fb-style-comments-actions button:active {
    text-decoration: underline;
}
.fb-style-comments-actions .fb-style-comments-like-btn.liked {
    color: #1877f2; 
}

.fb-style-comments-timestamp {
    /* margin-right: 8px; Уже установлено через .fb-style-comments-actions > * */
}
.fb-style-comments-likes-count {
    /* margin-left: 8px; Уже установлено через .fb-style-comments-actions > * */
    /* Fallback для старых WebView */
    display: inline-block;
    vertical-align: middle;
    /* Современный flexbox */
    display: -webkit-inline-box;
    display: -webkit-inline-flex;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
}
.fb-style-comments-likes-count .fb-style-comments-count {
    margin-left: 4px;
}

/* --- Replies --- */
.fb-style-comments-replies-container {
    margin-left: 46px; /* Отступ для ответов (ширина аватара + margin) */
    padding-top: 8px;
}

/* Ответ 1-го уровня */
.fb-style-comments-reply-item .fb-style-comments-avatar {
    width: 28px; 
    height: 28px;
    font-size: 0.8em;
    line-height: 28px;
}
.fb-style-comments-reply-item .fb-style-comments-comment-content {
     /* Заменяем calc() на фиксированное значение для совместимости */
     max-width: 82%; /* Примерно calc(100% - 38px) */
}

/* Ответ 2-го уровня (ответ на ответ) */
.fb-style-comments-replies-container .fb-style-comments-replies-container { 
    margin-left: 38px; /* Еще меньший отступ для следующего уровня */
}
.fb-style-comments-reply-item .fb-style-comments-reply-item .fb-style-comments-avatar { 
     width: 24px;
     height: 24px;
     font-size: 0.7em;
     line-height: 24px;
}
.fb-style-comments-reply-item .fb-style-comments-reply-item .fb-style-comments-comment-content {
    /* Заменяем calc() на фиксированное значение для совместимости */
    max-width: 80%; /* Примерно calc(100% - 34px) */
}


/* Форма ответа */
.fb-style-comments-reply-form-container {
    margin-top: 8px;
}
.fb-style-comments-reply-form-container .fb-style-comments-reply-form {
    /* Fallback для старых WebView */
    display: block;
    /* Современный flexbox с префиксами */
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: end;
    -webkit-align-items: flex-end;
    -ms-flex-align: end;
    align-items: flex-end;
}
.fb-style-comments-reply-form-container textarea {
    /* Fallback для старых WebView */
    width: 75%;
    display: inline-block;
    /* Современный flexbox */
    -webkit-box-flex: 1;
    -webkit-flex-grow: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    margin-right: 8px;
    padding: 8px 10px;
    border: 1px solid #ccd0d5;
    border-radius: 15px;
    font-size: 0.9em;
    background-color: #f5f6f7;
    min-height: 36px;
    line-height: 1.3;
    resize: none;
    overflow-y: hidden;
    max-height: 100px;
    -webkit-appearance: none;
    appearance: none;
    font-family: inherit;
}
.fb-style-comments-reply-form-container button.fb-style-comments-submit-btn {
     padding: 8px 12px;
     font-size: 0.85em;
     /* Fallback для старых WebView */
     display: inline-block;
     vertical-align: bottom;
     /* Современный flexbox */
     -webkit-flex-shrink: 0;
     -ms-flex-negative: 0;
     flex-shrink: 0;
     -webkit-align-self: flex-end;
     -ms-flex-item-align: end;
     align-self: flex-end;
     margin-left: 0;
}