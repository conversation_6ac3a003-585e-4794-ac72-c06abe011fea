<!DOCTYPE html>
<html lang="ru"> <!-- Изменено с es-AR на ru -->
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="format-detection" content="telephone=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">

    <!-- Оптимизация для Facebook WebView -->
    <meta property="fb:app_id" content="YOUR_FB_APP_ID"> <!-- Оставьте YOUR_FB_APP_ID или замените на ваш ID -->
    <meta property="og:type" content="article">
    <!-- Обновлено на основе нового заголовка H1 -->
    <meta property="og:title" content="Ведущий андролог: Новая формула для мужчин старше 55 лет ВДВОЕ эффективнее Виагры">
    <meta property="og:description" content="Американские исследователи разработали новый продукт, гарантирующий восстановление полной и естественной эрекции.">
    <meta property="og:image" content="https://example.com/news-image.jpg"> <!-- Оставьте ссылку или замените актуальным изображением -->
    <meta property="og:url" content="https://example.com/anmat-cosmeticos"> <!-- Оставьте ссылку или замените актуальным URL -->
    <meta property="og:site_name" content="Новости здоровья"> <!-- Адаптировано -->
    <meta property="article:published_time" content="2024-12-15T10:00:00Z"> <!-- Дата публикации, формат международный -->
    <meta property="article:author" content="Редакция"> <!-- Переведено -->
    <meta property="article:section" content="Здоровье"> <!-- Адаптировано/Переведено -->
    <meta property="article:tag" content="Мужское здоровье, потенция, эрекция, андрология"> <!-- Адаптированы теги под новый контент H1 -->

    <meta name="twitter:card" content="summary_large_image">
    <!-- Обновлено на основе нового заголовка H1 -->
    <meta name="twitter:title" content="Ведущий андролог: Новая формула для мужчин старше 55 лет ВДВОЕ эффективнее Виагры">
    <meta name="twitter:description" content="Американские исследователи разработали новый продукт, гарантирующий восстановление полной и естественной эрекции.">
    <meta name="twitter:image" content="https://example.com/news-image.jpg"> <!-- Оставьте ссылку или замените актуальным изображением -->

    <!-- Обновлено на основе нового заголовка H1 -->
    <title>Ведущий андролог: Новая формула для мужчин старше 55 | Новости</title>

    <link rel="stylesheet" href="media/style.css">
</head>
<body class="fb-optimized">
    <div class="container">

        <!-- Основное содержимое -->
        <main class="main-content">
            <!-- Заголовок статьи -->
            <div class="article-header">
                <div class="article-category light-label">
                     <img src="media/check.svg" alt="Проверенный факт" width="16" height="16" > <!-- Переведено alt -->
                    <span>Проверенный факт</span></div> <!-- Переведено -->

                    <div class="article-category">
                         <img src="media/shield-plus.svg" alt="Медицина" width="16" height="16" > <!-- Переведено alt -->
                    <span>Здоровье</span></div> <!-- Переведено -->

                <h1 class="article-title"><span>Уролог с 25-летним стажем: </span>Это лучший способ вернуть каменную эрекцию и здоровье простаты без гормональной терапии и операции
</h1> <!-- Переведено -->
                     <div class="author-info">
                        <img src="media/david.webp" alt="Доктор Дэйв Дэвид" class="author-avatar"> <!-- Переведено alt -->
                        <div class="author-details">
                            <div class="author-name">
                                <span>Доктор Дэйв Дэвид</span> <!-- Переведено -->
                                 <img src="media/verified.svg" alt="Проверенный специалист" width="16" height="16" > <!-- Переведено alt -->
                            </div>
                            <div class="meta-item article-date">
                                <div class="meta-text-block">
                                    <span>15 июня 2024</span> <!-- Переведено и адаптирован формат даты -->
                                </div>
                            </div>

                        </div>
                    </div>
                <p>Заболее чем 50 000 часов клинической практики в качестве уролога-андролога в Буэнос-Айресе, я видел боль и страх тысяч отчаявшихся мужчин, когда медицина была безсильна.
Но самое страшное – в 54 года я сам оказался в их шкуре. 
</p> <!-- Переведено -->


            </div>
            <div class="article-tags">
                          <div class="additional-meta">
                           <div class="meta-item article-content-rating">
                               <img src="media/star.svg" alt="Рейтинг" width="24" height="24" class="star-icon" loading="lazy">
                                <div class="meta-text-block">
                                    <span class="rating-value-text">4.78 / 5</span>
                                    <span>2,091 оценок</span>
                                </div>
                            </div>
                        <div class="meta-item article-comments-count">
                           <img src="media/comment.svg" alt="Комментарии" width="24" height="24" loading="lazy"> <!-- Исправлена опечатка в alt -->
                            <div class="meta-text-block">
                                <span>138</span>
                                <span>комментариев</span>
                            </div>
                        </div>

                    </div>
                </div>
            <!-- Главное изображение (видео) -->
                <div class="vsl-player-container" id="vslPlayer">
        <video id="previewVideo"
               src="media/preview.mp4"
               autoplay muted loop playsinline preload="auto">
            Ваш браузер не поддерживает тег video.
        </video>
        <video id="mainVideoPlayer"
               src="media/output_640p_crf28.mp4"
               preload="metadata" playsinline>
            Ваш браузер не поддерживает тег video.
        </video>
        <div class="vsl-control-icon" id="vslIcon">
            <!-- Содержимое будет вставлено JavaScript-ом -->
        </div>
    </div>
            <div class="image-caption">
               Рекомендовано доктором Дэйвом Дэвидом, выдающимся хирургом с мировым именем. <!-- Переведено -->
               <img
  src="media/media.svg"
  alt="Медиа логотипы"
  width="276"
  height="36"
  loading="lazy"
/> <!-- Адаптирован alt -->
            </div>

            <!-- Тело статьи -->
            <article class="article-body">
                <h2>В 2024 году в Аргентине 7 из 10 мужчин старше 55 лет, покончивших с собой, сделали это из-за проблем с потенцией</h2> <!-- Переведено -->
                <p>Национальное управление по лекарствам, пищевым продуктам и медицинским технологиям Аргентины (ANMAT) объявило о значительных изменениях в процессе импорта косметической продукции с целью усиления санитарного контроля и обеспечения безопасности аргентинских потребителей.</p> <!-- Переведено, ANMAT оставлено как есть, т.к. это название организации -->

                <h2>В 2024 году 7 из 10 мужчин старше 55 лет, совершивших суицид, сделали это из-за проблем с сексуальной потенцией</h2> <!-- Переведено -->

                <p>Среди наиболее заметных мер выделяются:</p> <!-- Переведено -->

                <ul>
                    <li>Новая документация, требуемая от импортеров</li> <!-- Переведено -->
                    <li>Более строгие проверки в пунктах ввоза</li> <!-- Переведено -->
                    <li>Увеличенные сроки для оценки продукции</li> <!-- Переведено -->
                    <li>Дополнительные сертификаты происхождения</li> <!-- Переведено -->
                </ul>

                <div class="quote-highlight">
                    "Эти меры направлены на защиту здоровья потребителей и обеспечение того, чтобы вся косметическая продукция, поступающая в страну, соответствовала самым высоким стандартам качества и безопасности."
                    <div class="quote-author">— Представитель ANMAT</div> <!-- Переведено -->
                </div>

                <h3>Влияние на рынок</h3> <!-- Переведено -->

                <p>Изменения окажут значительное влияние на рынок импортной косметики. Компаниям сектора придется адаптироваться к новым требованиям, что может временно вызвать некоторые корректировки в доступности определенных продуктов.</p> <!-- Переведено -->


                <div class="info-box">
                    <div class="info-box-title">Важная информация</div> <!-- Переведено -->
                    <p>Потребители могут ознакомиться со списком разрешенных продуктов на официальном сайте ANMAT.</p> <!-- Переведено -->
                </div>

                <h3>График внедрения</h3> <!-- Переведено -->

                <ol>
                    <li>Начальный этап: Уведомление импортеров (январь 2025)</li> <!-- Переведено -->
                    <li>Переходный период: Постепенная адаптация (февраль-март 2025)</li> <!-- Переведено -->
                    <li>Полное внедрение: Апрель 2025</li> <!-- Переведено -->
                </ol>

                <div class="warning-box info-box">
                    <div class="info-box-title">Вниманию импортеров</div> <!-- Переведено -->
                    <p>Крайне важно, чтобы компании как можно скорее начали процедуры адаптации к новому законодательству во избежание неудобств.</p> <!-- Переведено -->
                </div>

                <p>Эта мера является частью более широкой политики модернизации и укрепления аргентинской регуляторной системы, соответствующей международным стандартам контроля косметической продукции.</p> <!-- Переведено -->
            </article>

            <!-- Теги -->
            <div class="article-tags">
                <div class="tags-title">Теги</div> <!-- Переведено -->
                <a href="#" class="tag">ANMAT</a>
                <a href="#" class="tag">Косметика</a> <!-- Переведено -->
                <a href="#" class="tag">Импорт</a> <!-- Переведено -->
                <a href="#" class="tag">Здоровье</a> <!-- Переведено -->
                <a href="#" class="tag">Регулирование</a> <!-- Переведено -->
                <a href="#" class="tag">Аргентина</a> <!-- Переведено -->
            </div>

            <!-- Секция рейтинга - Alpine.js Компонент -->
            <div class="rating-section" x-data="commentsApp()" x-init="init()">
                <div class="rating-title">Оцените эту новость</div> <!-- Переведено -->
                <div class="rating-container">
                    <div class="stars">
                        <template x-for="star in 5" :key="star">
                            <span class="star"
                                  :class="{ 'active': star <= rating.userRating }"
                                  @click="setRating(star)"
                                  @touchstart.prevent="handleStarTouch(star, $event)"
                                  @touchend.prevent="setRating(star)">
                                <img src="media/star.svg" alt="Звезда" class="star-icon"> <!-- Переведено alt -->
                            </span>
                        </template>
                    </div>
                    <div class="rating-stats">
                        <span x-text="rating.average"></span> из 5 <!-- Переведено -->
                        (<span x-text="rating.total"></span> голосов) <!-- Переведено -->
                    </div>
                </div>

                <!-- Зарезервированное место для сообщения -->
                <div class="rating-feedback"
                     :class="{ 'visible': rating.feedback }"
                     :x-text="rating.feedback || 'Выберите вашу оценку'" <!-- Переведено -->
                     style="margin-top:10px; font-size:0.9em; text-align: center; min-height: 20px;"></div>

                <div class="rating-bars">
                    <template x-for="stars in getSortedStars()" :key="stars">
                        <div class="rating-bar">
                            <span class="bar-label" x-text="stars + '★'"></span>
                            <div class="bar-fill">
                                <div class="bar-progress"
                                     :id="'bar-' + stars"
                                     :class="{ 'animating': rating.animatingBar === stars }"
                                     :style="`width: ${(rating.breakdown[stars] / rating.total * 100).toFixed(1)}%`"></div>
                            </div>
                            <span class="bar-count"
                                  :id="'count-' + stars"
                                  :class="{ 'updated': rating.animatingBar === stars }"
                                  x-text="rating.breakdown[stars]"></span>
                        </div>
                    </template>
                </div>
            </div>
        </main>

        <!-- Секция комментариев - Alpine.js Компонент -->
        <section class="comments-section" x-data="commentsApp()" x-init="init()">
            <div class="comments-header">
                <h2 class="comments-title">Комментарии</h2> <!-- Переведено -->
                <!-- Для корректного склонения слова "комментарий" лучше использовать JS-функцию.
                     x-text="allComments.length + ' ' + pluralizeComments(allComments.length)"
                     где pluralizeComments(count) {
                        if (count % 10 === 1 && count % 100 !== 11) return 'комментарий';
                        if (count % 10 >= 2 && count % 10 <= 4 && (count % 100 < 10 || count % 100 >= 20)) return 'комментария';
                        return 'комментариев';
                     }
                     Пока оставлю простой вариант, как в оригинале.
                -->
                <div class="comments-count" x-text="allComments.length + (allComments.length === 1 ? ' комментарий' : (allComments.length > 1 && allComments.length < 5 ? ' комментария' : ' комментариев'))"></div> <!-- Адаптировано с упрощенным склонением -->
            </div>

            <!-- Форма комментария -->
            <div class="comment-form">
                <form @submit.prevent="addComment()" class="comment-form-main">
                    <!-- Поле имени -->
                    <input type="text" x-model="newComment.author" placeholder="Ваше имя и фамилия" required class="comment-name-input"> <!-- Переведено placeholder -->

                    <!-- Текстовое поле -->
                    <textarea x-model="newComment.text" placeholder="Напишите свой комментарий..." rows="2" required class="comment-textarea"></textarea> <!-- Переведено placeholder -->

                    <!-- Кнопка фото -->
                    <button type="button" @click="$refs.imageInput.click()" class="comment-photo-btn">
                        <img src="media/imageInput.svg" alt="Загрузить фото" class="upload-icon"> <!-- Переведено alt -->
                        Добавить фото <!-- Переведено -->
                    </button>
                    <input type="file" @change="handleImageUpload" accept="image/*" style="display: none;" x-ref="imageInput">

                    <!-- Превью фото -->
                    <div x-show="newComment.image" class="comment-image-preview">
                        <img :src="newComment.image" alt="Предпросмотр"> <!-- Переведено alt -->
                        <button type="button" @click="removeImage()" class="comment-remove-image">×</button>
                    </div>

                    <!-- Кнопка отправки -->
                    <button type="submit" class="comment-submit">Опубликовать комментарий</button> <!-- Переведено -->
                </form>
            </div>

            <!-- Список комментариев -->
            <div class="comments-list">
                <template x-for="comment in getVisibleComments()" :key="comment.id">
                    <div class="comment" :data-comment-id="comment.id">
                        <div class="comment-header">
                            <div class="comment-avatar"
                                 :style="comment.isCompany ? 'background-image: url(' + comment.avatarLogo + '); background-size: cover;' : 'background-color: ' + comment.avatarColor"
                                 x-text="comment.isCompany ? '' : getInitials(comment.author)"></div>
                            <div class="comment-meta">
                                <div class="comment-author" x-text="comment.author"></div>
                                <div class="comment-time" x-text="comment.timestamp"></div>
                            </div>
                        </div>
                        <div class="comment-text" x-html="processText(comment.text)"></div>

                        <!-- Изображение комментария -->
                        <div x-show="comment.image" class="comment-image">
                            <img :src="comment.image" alt="Изображение комментария" class="comment-full-width-image" loading="lazy"> <!-- Переведено alt -->
                        </div>

                        <div class="comment-actions">
                            <button class="comment-action"
                                    :class="{ 'liked': comment.userLiked }"
                                    @click="toggleLike(comment)"
                                    @touchstart.prevent="handleButtonTouch($event)"
                                    @touchend.prevent="toggleLike(comment)">
                                <img src="media/like.svg" alt="Нравится" class="like-icon"> <!-- Переведено alt -->
                                <span>Нравится</span> <!-- Переведено -->
                                <span class="comment-count" x-text="comment.likes"></span>
                            </button>
                            <button class="comment-action"
                                    :class="{ 'disliked': comment.userDisliked }"
                                    @click="toggleDislike(comment)"
                                    @touchstart.prevent="handleButtonTouch($event)"
                                    @touchend.prevent="toggleDislike(comment)">
                                <img src="media/like.svg" alt="Не нравится" class="dislike-icon"> <!-- Переведено alt -->
                                <span>Не нравится</span> <!-- Переведено -->
                                <span class="comment-count" x-text="comment.dislikes"></span>
                            </button>
                            <button class="comment-action"
                                    @click="toggleReplyForm(comment)"
                                    @touchstart.prevent="handleButtonTouch($event)"
                                    @touchend.prevent="toggleReplyForm(comment)">Ответить</button> <!-- Переведено -->
                        </div>

                        <!-- Форма ответа -->
                        <div x-show="comment.showReplyForm" class="comment-reply-form-container">
                            <form @submit.prevent="addReply(comment)">
                                <input type="text" x-model="comment.replyAuthor" placeholder="Ваше имя" required class="reply-name-input"> <!-- Переведено placeholder -->
                                <textarea x-model="comment.replyText" placeholder="Напишите ответ..." rows="1" required class="reply-textarea"></textarea> <!-- Переведено placeholder -->
                                <button type="submit" class="comment-submit">Ответить</button> <!-- Переведено -->
                            </form>
                        </div>

                        <!-- Ответы -->
                        <div x-show="comment.replies && comment.replies.length > 0" class="comment-replies">
                            <template x-for="reply in comment.replies" :key="reply.id">
                                <div class="reply" :data-comment-id="reply.id">
                                    <div class="comment-header">
                                        <div class="comment-avatar"
                                             :style="reply.isCompany ? 'background-image: url(' + reply.avatarLogo + '); background-size: cover;' : 'background-color: ' + reply.avatarColor"
                                             x-text="reply.isCompany ? '' : getInitials(reply.author)"></div>
                                        <div class="comment-meta">
                                            <div class="comment-author" x-text="reply.author"></div>
                                            <div class="comment-time" x-text="reply.timestamp"></div>
                                        </div>
                                    </div>
                                    <div class="comment-text" x-html="processText(reply.text)"></div>

                                    <!-- Изображение ответа -->
                                    <div x-show="reply.image" class="comment-image">
                                        <img :src="reply.image" alt="Изображение ответа" class="comment-full-width-image" loading="lazy"> <!-- Переведено alt -->
                                    </div>

                                    <div class="comment-actions">
                                        <button class="comment-action"
                                                :class="{ 'liked': reply.userLiked }"
                                                @click="toggleLike(reply)"
                                                @touchstart.prevent="handleButtonTouch($event)"
                                                @touchend.prevent="toggleLike(reply)">
                                            <img src="media/like.svg" alt="Нравится" class="like-icon"> <!-- Переведено alt -->
                                            <span>Нравится</span> <!-- Переведено -->
                                            <span class="comment-count" x-text="reply.likes"></span>
                                        </button>
                                        <button class="comment-action"
                                                :class="{ 'disliked': reply.userDisliked }"
                                                @click="toggleDislike(reply)"
                                                @touchstart.prevent="handleButtonTouch($event)"
                                                @touchend.prevent="toggleDislike(reply)">
                                            <img src="media/like.svg" alt="Не нравится" class="dislike-icon"> <!-- Переведено alt -->
                                            <span>Не нравится</span> <!-- Переведено -->
                                            <span class="comment-count" x-text="reply.dislikes"></span>
                                        </button>
                                        <!-- Кнопка "Ответить" убрана для ответов -->
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                </template>
            </div>

            <!-- Кнопка "Показать еще" -->
            <!-- Для корректного склонения слова "комментарий" лучше использовать JS-функцию, как описано выше -->
            <div x-show="hasMoreComments()" class="load-more-container">
                <button @click="loadMoreComments()"
                        @touchstart.prevent="handleButtonTouch($event)"
                        @touchend.prevent="loadMoreComments()"
                        class="load-more-btn">
                    <span x-text="'Показать еще ' + Math.min(10, getRemainingCommentsCount()) + (Math.min(10, getRemainingCommentsCount()) === 1 ? ' комментарий' : (Math.min(10, getRemainingCommentsCount()) > 1 && Math.min(10, getRemainingCommentsCount()) < 5 ? ' комментария' : ' комментариев'))"></span> <!-- Адаптировано с упрощенным склонением -->
                    <span class="remaining-count" x-text="'(осталось ' + getRemainingCommentsCount() + ')'"></span> <!-- Переведено -->
                </button>
            </div>
        </section>
          <a class="go-order" href="#order" role="button" style="display: none;">
            Воспользуйтесь СКИДКОЙ 50%! <!-- Переведено и адаптировано -->
          </a>
    </div>

    <!-- Данные комментариев -->
    <!-- Убедитесь, что файл comments-data.js также переведен, если он содержит текстовые данные -->
    <script src="comments-data.js"></script>
    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <!-- Убедитесь, что файл script.js адаптирован, если он генерирует текст или требует локализации -->
    <script src="script.js"></script>

</body>
</html>