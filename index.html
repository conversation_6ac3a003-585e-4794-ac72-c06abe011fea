<!DOCTYPE html>
<html lang="es-AR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="format-detection" content="telephone=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    
    <!-- Facebook WebView optimizations -->
    <meta property="fb:app_id" content="YOUR_FB_APP_ID">
    <meta property="og:type" content="article">
    <meta property="og:title" content="ANMAT dispuso cambio en importación de cosméticos">
    <meta property="og:description" content="La autoridad sanitaria implementó nuevas medidas para el control de productos cosméticos importados">
    <meta property="og:image" content="https://example.com/news-image.jpg">
    <meta property="og:url" content="https://example.com/anmat-cosmeticos">
    <meta property="og:site_name" content="Noticias Argentina">
    <meta property="article:published_time" content="2024-12-15T10:00:00Z">
    <meta property="article:author" content="Redacción">
    <meta property="article:section" content="Sociedad">
    <meta property="article:tag" content="ANMAT, cosméticos, importación, salud">
    
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="ANMAT dispuso cambio en importación de cosméticos">
    <meta name="twitter:description" content="La autoridad sanitaria implementó nuevas medidas para el control de productos cosméticos importados">
    <meta name="twitter:image" content="https://example.com/news-image.jpg">
    
    <title>ANMAT dispuso cambio en importación de cosméticos | Noticias</title>

    <link rel="stylesheet" href="media/style.css">
    <!-- Данные комментариев -->
    <script src="comments-data.js"></script>
    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
</head>
<body class="fb-optimized">
    <div class="container">
       
     
        
        <!-- Contenido principal -->
        <main class="main-content">
            <!-- Encabezado del artículo -->
            <div class="article-header">
                <div class="article-category light-label"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="#0D47A1" stroke="currentColor" stroke-width="2.25" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-facebook-icon lucide-facebook"><path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"/></svg><span>Info Médica Confiable</span></div>
          
                
                <h1 class="article-title"><span>Andrólogo líder:</span><br>Para hombres mayores de 55 años, la nueva fórmula es el DOBLE de efectiva que Viagra</h1>
                     <div class="author-info">
                        <img src="media/david.webp" alt="Dr. Dave David" class="author-avatar">
                        <div class="author-details">
                            <div class="author-name">
                                <span>Dr. Dave David</span>
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_3552_2840)"> <path fill-rule="evenodd" clip-rule="evenodd" d="M11.5134 6.58948L7.20571 10.8972C7.14867 10.9543 7.08095 10.9995 7.00641 11.0304C6.93186 11.0613 6.85195 11.0772 6.77125 11.0772C6.69056 11.0772 6.61065 11.0613 6.5361 11.0304C6.46155 10.9995 6.39383 10.9543 6.3368 10.8972L4.49064 9.05102C4.43179 8.99434 4.38481 8.92646 4.35246 8.85143C4.3201 8.77639 4.30303 8.69568 4.3022 8.61397C4.30138 8.53226 4.31683 8.45121 4.34767 8.37554C4.3785 8.29987 4.4241 8.2311 4.4818 8.17324C4.53949 8.11538 4.60813 8.0696 4.68371 8.03855C4.75929 8.0075 4.8403 7.99183 4.92201 7.99243C5.00372 7.99302 5.08449 8.00986 5.15961 8.04201C5.23474 8.07415 5.3027 8.12094 5.35956 8.17963L6.77125 9.59136L10.6445 5.71809C10.7013 5.6594 10.7693 5.61261 10.8444 5.58047C10.9196 5.54833 11.0003 5.53148 11.082 5.53089C11.1637 5.5303 11.2448 5.54596 11.3203 5.57701C11.3959 5.60806 11.4646 5.65384 11.5222 5.7117C11.5799 5.76956 11.6255 5.83833 11.6564 5.914C11.6872 5.98967 11.7027 6.07073 11.7018 6.15243C11.701 6.23414 11.6839 6.31486 11.6516 6.38989C11.6192 6.46492 11.5723 6.5328 11.5134 6.58948ZM15.3017 9.41408L14.5768 7.99994L15.3005 6.58452C15.3885 6.41279 15.4097 6.21456 15.3599 6.02811C15.3101 5.84166 15.1929 5.68032 15.0309 5.57536L13.6956 4.71381L13.6156 3.12982C13.6083 2.93594 13.5283 2.75188 13.3914 2.61435C13.2546 2.47681 13.0709 2.39585 12.8771 2.38763L11.2894 2.30643L10.4242 0.968538C10.3183 0.807653 10.1568 0.69149 9.97058 0.642216C9.78439 0.592941 9.58656 0.614039 9.41495 0.701486L8.00202 1.42519L6.58664 0.701486C6.41488 0.612983 6.2163 0.591696 6.02968 0.641765C5.84306 0.691834 5.6818 0.809627 5.57741 0.972219L4.71464 2.30763L3.12818 2.38763C2.93485 2.39551 2.75156 2.4758 2.61474 2.61262C2.47792 2.74944 2.39759 2.93274 2.38972 3.12606L2.30848 4.71381L0.96941 5.57776C0.808307 5.68348 0.692011 5.84508 0.642928 6.03141C0.593845 6.21775 0.615442 6.41564 0.70356 6.587L1.42725 8.00114L0.70356 9.41656C0.615582 9.5881 0.594441 9.7862 0.644243 9.97244C0.694045 10.1587 0.811244 10.3198 0.9731 10.4245L2.30848 11.2873L2.38849 12.8701C2.40572 13.2762 2.72572 13.5962 3.12695 13.6134L4.71464 13.6934L5.57987 15.0325C5.79771 15.3648 6.23217 15.4781 6.5891 15.2984L8.00202 14.5747L9.41741 15.2997C9.58905 15.3887 9.78782 15.4103 9.97459 15.3602C10.1614 15.3101 10.3226 15.1919 10.4266 15.0289L11.2894 13.6934L12.8722 13.6147C13.0665 13.6078 13.251 13.5277 13.3888 13.3906C13.5266 13.2534 13.6076 13.0693 13.6156 12.875L13.6956 11.2873L15.0346 10.4221C15.1956 10.3166 15.3119 10.1553 15.361 9.96914C15.4101 9.78301 15.3898 9.58525 15.3017 9.41408Z" fill="#0072C6"/> </g> <defs> <clipPath id="clip0_3552_2840"> <rect width="16" height="16" fill="white"/> </clipPath> </defs> </svg>
                            </div>
                            <div class="meta-item article-date">
                                <div class="meta-text-block">
                                    <span>15 jun. 2024</span>
                                </div>
                            </div>

                        </div>
                    </div>
                <p>Investigadores estadounidenses desarrollaron un nuevo producto que garantiza la <span>restauración de una erección plena y natural, como si tuviera 22 años otra vez</span>, sin tratamientos hormonales ni cirugía.
</p>
                               <div class="article-meta">
               

                      
                    <div class="additional-meta">
                           <div class="meta-item article-content-rating">
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" class="meta-icon lucide lucide-star rating-star-icon"><polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"/></svg>
                                <div class="meta-text-block">
                                    <span class="rating-value-text">4.78 / 5</span>
                                    <span>3,791 оценок</span>
                                </div>
                            </div>
                        <div class="meta-item article-comments-count"> <!-- Изменено имя класса для ясности -->
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" class="meta-icon lucide lucide-message-circle"><path d="M7.9 20A9 9 0 1 0 4 16.1L2 22Z"/></svg>
                            <div class="meta-text-block">
                                <span>47</span>
                                <span>комментариев</span>
                            </div>
                        </div>
                  
                    </div>
                </div>
            </div>
            
            <!-- Imagen principal -->
                <div class="vsl-player-container" id="vslPlayer">
        <video id="previewVideo"
               src="media/preview.mp4"
               autoplay muted loop playsinline preload="auto">
            Ваш браузер не поддерживает тег video.
        </video>
        <video id="mainVideoPlayer"
               src="media/output_640p_crf28.mp4"
               preload="metadata" playsinline>
            Ваш браузер не поддерживает тег video.
        </video>
        <div class="vsl-control-icon" id="vslIcon">
            <!-- Содержимое будет вставлено JavaScript-ом -->
        </div>
    </div>
            <div class="image-caption">
               Recomendado por el Dr. Dave David, eminente cirujano de fama mundial.
               <img 
  src="media/media.svg" 
  alt="Описание изображения" 
  width="276" 
  height="36" 
  loading="lazy"
/>
            </div>
            
            <!-- Cuerpo del artículo -->
            <article class="article-body">
                <h2>Durante 2024 en Argentina, 7 de cada 10 hombres mayores de 55 que se quitaron la vida fue por problemas de potencia</h2>
                <p>La Administración Nacional de Medicamentos, Alimentos y Tecnología Médica (ANMAT) dispuso importantes cambios en el proceso de importación de productos cosméticos, con el objetivo de fortalecer los controles sanitarios y garantizar la seguridad de los consumidores argentinos.</p>
                
                <h2>En 2024, 7 de cada 10 hombres mayores de 55 que se suicidaron lo hicieron por problemas de potencial sexual</h2>
                
                <p>Entre las medidas más destacadas se encuentran:</p>
                
                <ul>
                    <li>Nueva documentación requerida para importadores</li>
                    <li>Inspecciones más rigurosas en puntos de ingreso</li>
                    <li>Plazos extendidos para evaluación de productos</li>
                    <li>Certificaciones adicionales de origen</li>
                </ul>
                
                <div class="quote-highlight">
                    "Estas medidas buscan proteger la salud de los consumidores y asegurar que todos los productos cosméticos que ingresan al país cumplan con los más altos estándares de calidad y seguridad."
                    <div class="quote-author">— Representante de ANMAT</div>
                </div>
                
                <h3>Impacto en el mercado</h3>
                
                <p>Los cambios tendrán un impacto significativo en el mercado de cosméticos importados. Las empresas del sector deberán adaptarse a los nuevos requisitos, lo que podría generar temporalmente algunos ajustes en la disponibilidad de ciertos productos.</p>
                
         
                
                <div class="info-box">
                    <div class="info-box-title">Información importante</div>
                    <p>Los consumidores pueden consultar la lista de productos autorizados en el sitio web oficial de ANMAT.</p>
                </div>
                
                <h3>Cronograma de implementación</h3>
                
                <ol>
                    <li>Fase inicial: Notificación a importadores (enero 2025)</li>
                    <li>Período de transición: Adaptación gradual (febrero-marzo 2025)</li>
                    <li>Implementación completa: Abril 2025</li>
                </ol>
                
                <div class="warning-box info-box">
                    <div class="info-box-title">Atención importadores</div>
                    <p>Es fundamental que las empresas inicien cuanto antes los trámites de adecuación a la nueva normativa para evitar inconvenientes.</p>
                </div>
                
                <p>La medida forma parte de una política más amplia de modernización y fortalecimiento del sistema regulatorio argentino, alineándose con estándares internacionales de control de productos cosméticos.</p>
            </article>
            
            <!-- Tags -->
            <div class="article-tags">
                <div class="tags-title">Etiquetas</div>
                <a href="#" class="tag">ANMAT</a>
                <a href="#" class="tag">Cosméticos</a>
                <a href="#" class="tag">Importación</a>
                <a href="#" class="tag">Salud</a>
                <a href="#" class="tag">Regulación</a>
                <a href="#" class="tag">Argentina</a>
            </div>
            
            <!-- Rating Section - Alpine.js Component -->
            <div class="rating-section" x-data="commentsApp()" x-init="init()">
                <div class="rating-title">Califica esta noticia</div>
                <div class="rating-container">
                    <div class="stars">
                        <template x-for="star in 5" :key="star">
                            <span class="star"
                                  :class="{ 'active': star <= rating.userRating }"
                                  @click="setRating(star)"
                                  @touchstart.prevent="handleStarTouch(star, $event)"
                                  @touchend.prevent="setRating(star)">
                                <img src="img_coment/star.svg" alt="Star" class="star-icon">
                            </span>
                        </template>
                    </div>
                    <div class="rating-stats">
                        <span x-text="rating.average"></span> de 5
                        (<span x-text="rating.total"></span> votos)
                    </div>
                </div>

                <!-- Зарезервированное место для сообщения -->
                <div class="rating-feedback"
                     :class="{ 'visible': rating.feedback }"
                     x-text="rating.feedback || 'Selecciona tu calificación'"
                     style="margin-top:10px; font-size:0.9em; text-align: center; min-height: 20px;"></div>

                <div class="rating-bars">
                    <template x-for="stars in getSortedStars()" :key="stars">
                        <div class="rating-bar">
                            <span class="bar-label" x-text="stars + '★'"></span>
                            <div class="bar-fill">
                                <div class="bar-progress"
                                     :id="'bar-' + stars"
                                     :class="{ 'animating': rating.animatingBar === stars }"
                                     :style="`width: ${(rating.breakdown[stars] / rating.total * 100).toFixed(1)}%`"></div>
                            </div>
                            <span class="bar-count"
                                  :id="'count-' + stars"
                                  :class="{ 'updated': rating.animatingBar === stars }"
                                  x-text="rating.breakdown[stars]"></span>
                        </div>
                    </template>
                </div>
            </div>
        </main>
        
        <!-- Sección de comentarios - Alpine.js Component -->
        <section class="comments-section" x-data="commentsApp()" x-init="init()">
            <div class="comments-header">
                <h2 class="comments-title">Comentarios</h2>
                <div class="comments-count" x-text="allComments.length + ' comentarios'"></div>
            </div>

            <!-- Formulario de comentario -->
            <div class="comment-form">
                <form @submit.prevent="addComment()" class="comment-form-main">
                    <!-- Поле имени -->
                    <input type="text" x-model="newComment.author" placeholder="Tu nombre y apellido" required class="comment-name-input">

                    <!-- Текстовое поле -->
                    <textarea x-model="newComment.text" placeholder="Escribe tu comentario..." rows="2" required class="comment-textarea"></textarea>

                    <!-- Кнопка фото -->
                    <button type="button" @click="$refs.imageInput.click()" class="comment-photo-btn">
                        <img src="img_coment/imageInput.svg" alt="Upload" class="upload-icon">
                        Agregar foto
                    </button>
                    <input type="file" @change="handleImageUpload" accept="image/*" style="display: none;" x-ref="imageInput">

                    <!-- Превью фото -->
                    <div x-show="newComment.image" class="comment-image-preview">
                        <img :src="newComment.image" alt="Preview">
                        <button type="button" @click="removeImage()" class="comment-remove-image">×</button>
                    </div>

                    <!-- Кнопка отправки -->
                    <button type="submit" class="comment-submit">Publicar comentario</button>
                </form>
            </div>
            
            <!-- Lista de comentarios dinámicos -->
            <div class="comments-list">
                <template x-for="comment in allComments" :key="comment.id">
                    <div class="comment">
                        <div class="comment-header">
                            <div class="comment-avatar"
                                 :style="comment.isCompany ? 'background-image: url(' + comment.avatarLogo + '); background-size: cover;' : 'background-color: ' + comment.avatarColor"
                                 x-text="comment.isCompany ? '' : comment.author.split(' ').map(n => n[0]).join('').substring(0, 2)"></div>
                            <div class="comment-meta">
                                <div class="comment-author" x-text="comment.author"></div>
                                <div class="comment-time" x-text="comment.timestamp"></div>
                            </div>
                        </div>
                        <div class="comment-text" x-text="comment.text"></div>

                        <!-- Imagen del comentario -->
                        <div x-show="comment.image" class="comment-image">
                            <img :src="comment.image" alt="Comment image" style="max-width: 200px; border-radius: 8px;">
                        </div>

                        <div class="comment-actions">
                            <button class="comment-action"
                                    :class="{ 'liked': comment.userLiked }"
                                    @click="toggleLike(comment)"
                                    @touchstart.prevent="handleButtonTouch($event)"
                                    @touchend.prevent="toggleLike(comment)">
                                <img src="img_coment/like.svg" alt="Like" class="like-icon">
                                <span x-text="comment.userLiked ? 'Ya no me gusta' : 'Me gusta'"></span>
                                <span class="comment-count" x-text="comment.likes"></span>
                            </button>
                            <button x-show="getDepth(comment) < 2"
                                    class="comment-action"
                                    @click="toggleReplyForm(comment)"
                                    @touchstart.prevent="handleButtonTouch($event)"
                                    @touchend.prevent="toggleReplyForm(comment)">Responder</button>
                        </div>

                        <!-- Форма ответа -->
                        <div x-show="comment.showReplyForm" class="comment-reply-form-container">
                            <form @submit.prevent="addReply(comment)">
                                <input type="text" x-model="comment.replyAuthor" placeholder="Tu nombre" required class="reply-name-input">
                                <textarea x-model="comment.replyText" placeholder="Escribe una respuesta..." rows="1" required class="reply-textarea"></textarea>
                                <button type="submit" class="comment-submit">Responder</button>
                            </form>
                        </div>

                        <!-- Respuestas -->
                        <div x-show="comment.replies && comment.replies.length > 0" class="comment-replies">
                            <template x-for="reply in comment.replies" :key="reply.id">
                                <div class="reply">
                                    <div class="comment-header">
                                        <div class="comment-avatar"
                                             :style="reply.isCompany ? 'background-image: url(' + reply.avatarLogo + '); background-size: cover;' : 'background-color: ' + reply.avatarColor"
                                             x-text="reply.isCompany ? '' : reply.author.split(' ').map(n => n[0]).join('').substring(0, 2)"></div>
                                        <div class="comment-meta">
                                            <div class="comment-author" x-text="reply.author"></div>
                                            <div class="comment-time" x-text="reply.timestamp"></div>
                                        </div>
                                    </div>
                                    <div class="comment-text" x-text="reply.text"></div>

                                    <!-- Imagen del reply -->
                                    <div x-show="reply.image" class="comment-image">
                                        <img :src="reply.image" alt="Reply image" style="max-width: 200px; border-radius: 8px;">
                                    </div>

                                    <div class="comment-actions">
                                        <button class="comment-action"
                                                :class="{ 'liked': reply.userLiked }"
                                                @click="toggleLike(reply)"
                                                @touchstart.prevent="handleButtonTouch($event)"
                                                @touchend.prevent="toggleLike(reply)">
                                            <img src="img_coment/like.svg" alt="Like" class="like-icon">
                                            <span x-text="reply.userLiked ? 'Ya no me gusta' : 'Me gusta'"></span>
                                            <span class="comment-count" x-text="reply.likes"></span>
                                        </button>
                                        <button x-show="getDepth(reply) < 2"
                                                class="comment-action"
                                                @click="toggleReplyForm(reply)"
                                                @touchstart.prevent="handleButtonTouch($event)"
                                                @touchend.prevent="toggleReplyForm(reply)">Responder</button>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                </template>
            </div>
        </section>
          <a class="go-order" href="#order" role="button">
            ¡Aprovechá el 50% OFF!
          </a>
    </div>
    
    <script>
        // Alpine.js компонент для комментариев и рейтинга
        function commentsApp() {
            return {
                // Рейтинг
                rating: {
                    userRating: 0,
                    previousUserRating: 0,
                    average: 4.8,
                    total: 3791,
                    breakdown: { 5: 3150, 4: 500, 3: 100, 2: 25, 1: 16 },
                    feedback: '',
                    userHasVoted: false,
                    justUpdated: false,
                    animatingBar: null
                },

                // Комментарии (загружаются через parseCommentsData)
                allComments: [],

                // Новый комментарий
                newComment: {
                    author: '',
                    text: '',
                    image: null
                },

                // Методы
                init: function() {
                    // Загружаем сохраненные данные
                    const savedRating = this.getFromStorage('userRating');
                    if (savedRating) {
                        this.rating.userRating = savedRating;
                        this.rating.previousUserRating = savedRating;
                        this.rating.userHasVoted = true;
                        this.rating.feedback = `Tu calificación anterior: ${savedRating} ${savedRating === 1 ? 'estrella' : 'estrellas'}. Puedes cambiarla.`;
                    }

                    const savedAuthor = this.getFromStorage('authorName');
                    if (savedAuthor) {
                        this.newComment.author = savedAuthor;
                    }

                    // Загружаем комментарии
                    this.loadTestComments();
                },

                // Рейтинг методы
                setRating: function(stars) {
                    var self = this;

                    // Если пользователь уже голосовал, показываем удаление старого голоса
                    if (this.rating.userHasVoted && this.rating.previousUserRating > 0) {
                        // Сначала анимируем убирание старого голоса
                        this.rating.animatingBar = this.rating.previousUserRating;
                        this.rating.breakdown[this.rating.previousUserRating]--;
                        this.updateAverageRating();

                        // Через 600ms добавляем новый голос
                        setTimeout(function() {
                            self.addNewRating(stars);
                        }, 600);
                    } else {
                        // Если первый раз голосует, сразу добавляем
                        this.addNewRating(stars);
                    }
                },

                addNewRating: function(stars) {
                    var self = this;

                    // Сохраняем состояние до изменений
                    var wasFirstVote = !this.rating.userHasVoted;

                    // Добавляем новый голос
                    this.rating.breakdown[stars]++;

                    // Увеличиваем total только если это первый голос пользователя
                    if (wasFirstVote) {
                        this.rating.total++;
                    }

                    // Обновляем состояние
                    this.rating.previousUserRating = this.rating.userRating;
                    this.rating.userRating = stars;
                    this.rating.userHasVoted = true;

                    // Пересчитываем средний рейтинг
                    this.updateAverageRating();

                    // Запускаем анимации для новой звезды
                    this.rating.justUpdated = true;
                    this.rating.animatingBar = stars;

                    this.rating.feedback = '¡Gracias por tu calificación!';
                    this.saveToStorage('userRating', stars);

                    // Убираем флаги анимации через время
                    setTimeout(function() {
                        self.rating.justUpdated = false;
                        self.rating.animatingBar = null;
                    }, 1200);
                },

                updateAverageRating: function() {
                    var total = 0;
                    var sum = 0;
                    for (var star in this.rating.breakdown) {
                        var count = this.rating.breakdown[star];
                        total += count;
                        sum += star * count;
                    }
                    this.rating.total = total;
                    this.rating.average = total > 0 ? (sum / total).toFixed(1) : 0;
                },

                getSortedStars: function() {
                    // Сортируем звезды по количеству голосов (больше голосов = выше в списке)
                    var self = this;
                    var keys = Object.keys(this.rating.breakdown);
                    var sorted = keys.sort(function(a, b) {
                        return self.rating.breakdown[b] - self.rating.breakdown[a];
                    });
                    var result = [];
                    for (var i = 0; i < sorted.length; i++) {
                        result.push(Number(sorted[i]));
                    }
                    return result;
                },

                // Комментарии методы
                addComment: function() {
                    if (!this.newComment.author.trim() || !this.newComment.text.trim()) return;

                    var comment = {
                        id: this.generateId(),
                        author: this.newComment.author,
                        text: this.newComment.text,
                        image: this.newComment.image,
                        timestamp: 'Ahora mismo',
                        likes: 0,
                        userLiked: false,
                        avatarColor: this.getRandomColor(),
                        isCompany: false,
                        replies: [],
                        showReplyForm: false,
                        replyAuthor: '',
                        replyText: ''
                    };

                    this.allComments.unshift(comment);
                    this.saveToStorage('authorName', this.newComment.author);
                    this.saveToStorage('allComments', this.allComments);

                    // Очищаем форму
                    this.newComment.text = '';
                    this.newComment.image = null;
                },

                addReply: function(parentComment) {
                    if (!parentComment.replyAuthor.trim() || !parentComment.replyText.trim()) return;

                    var reply = {
                        id: this.generateId(),
                        author: parentComment.replyAuthor,
                        text: parentComment.replyText,
                        image: null,
                        timestamp: 'Ahora mismo',
                        likes: 0,
                        userLiked: false,
                        avatarColor: this.getRandomColor(),
                        isCompany: false,
                        replies: [],
                        showReplyForm: false,
                        replyAuthor: '',
                        replyText: ''
                    };

                    parentComment.replies.push(reply);
                    parentComment.showReplyForm = false;
                    parentComment.replyAuthor = '';
                    parentComment.replyText = '';
                    this.saveToStorage('allComments', this.allComments);
                },

                toggleLike: function(comment) {
                    comment.userLiked = !comment.userLiked;
                    comment.likes += comment.userLiked ? 1 : -1;
                    comment.lastLikeTime = new Date().toISOString();
                    this.saveToStorage('allComments', this.allComments);
                },

                toggleReplyForm: function(comment) {
                    comment.showReplyForm = !comment.showReplyForm;
                    if (!comment.showReplyForm) {
                        comment.replyAuthor = '';
                        comment.replyText = '';
                    }
                },

                getDepth: function(comment) {
                    return 1; // Простая реализация для 2 уровней
                },

                // Вспомогательные методы
                getRandomColor: function() {
                    var colors = ['#FFC300', '#DAF7A6', '#581845', '#3498DB', '#E74C3C', '#9B59B6', '#F39C12', '#27AE60'];
                    return colors[Math.floor(Math.random() * colors.length)];
                },

                generateId: function() {
                    return Date.now().toString(36) + Math.random().toString(36).substring(2);
                },

                handleImageUpload: function(event) {
                    var file = event.target.files[0];
                    if (file && file.type.indexOf('image/') === 0) {
                        var self = this;
                        var reader = new FileReader();
                        reader.onload = function(e) {
                            self.newComment.image = e.target.result;
                        };
                        reader.readAsDataURL(file);
                    }
                },

                removeImage: function() {
                    this.newComment.image = null;
                },

                // Touch события для мобильных устройств
                handleStarTouch: function(star, event) {
                    // Визуальная обратная связь при touch
                    event.target.style.transform = 'scale(1.1)';
                    var self = this;
                    setTimeout(function() {
                        event.target.style.transform = 'scale(1)';
                    }, 150);
                },

                handleButtonTouch: function(event) {
                    // Визуальная обратная связь для кнопок
                    event.target.style.opacity = '0.7';
                    var self = this;
                    setTimeout(function() {
                        event.target.style.opacity = '1';
                    }, 150);
                },

                // Хранение данных
                saveToStorage: function(key, value) {
                    try {
                        // Пробуем sessionStorage
                        if (typeof sessionStorage !== 'undefined') {
                            sessionStorage.setItem(key, JSON.stringify(value));
                            return;
                        }
                    } catch (e) {
                        // Игнорируем ошибку
                    }

                    try {
                        // Fallback на cookies для WebView
                        var expires = '';
                        var date = new Date();
                        date.setTime(date.getTime() + (1 * 24 * 60 * 60 * 1000)); // 1 день
                        expires = '; expires=' + date.toUTCString();
                        document.cookie = 'fb_' + key + '=' + JSON.stringify(value) + expires + '; path=/';
                    } catch (e2) {
                        console.warn('Storage not available');
                    }
                },

                getFromStorage: function(key) {
                    try {
                        // Пробуем sessionStorage
                        if (typeof sessionStorage !== 'undefined') {
                            var stored = sessionStorage.getItem(key);
                            return stored ? JSON.parse(stored) : null;
                        }
                    } catch (e) {
                        // Игнорируем ошибку
                    }

                    try {
                        // Fallback на cookies
                        var nameEQ = 'fb_' + key + '=';
                        var ca = document.cookie.split(';');
                        for (var i = 0; i < ca.length; i++) {
                            var c = ca[i];
                            while (c.charAt(0) === ' ') c = c.substring(1, c.length);
                            if (c.indexOf(nameEQ) === 0) {
                                return JSON.parse(c.substring(nameEQ.length, c.length));
                            }
                        }
                    } catch (e2) {
                        // Игнорируем ошибку
                    }

                    return null;
                },

                // Загрузка комментариев
                loadTestComments: function() {
                    // Все данные из comments-data.txt встроены сюда
                    var allCommentsData = window.COMMENTS_DATA || this.getDefaultComments();
                    this.parseCommentsData(allCommentsData);
                },

                parseCommentsData: function(textData) {
                    var comments = [];
                    var lines = textData.split('\n');
                    var currentComment = null;
                    var currentReply = null;

                    for (var i = 0; i < lines.length; i++) {
                        var line = lines[i].trim();

                        // Пропускаем пустые строки и комментарии
                        if (!line || line.startsWith('#')) {
                            continue;
                        }

                        if (line === '[COMMENT]') {
                            // Сохраняем предыдущий комментарий
                            if (currentComment) {
                                comments.push(currentComment);
                            }
                            // Создаем новый комментарий
                            currentComment = {
                                id: this.generateId(),
                                author: '',
                                text: '',
                                image: null,
                                likes: 0,
                                timestamp: '',
                                userLiked: false,
                                avatarColor: this.getRandomColor(),
                                isCompany: false,
                                replies: [],
                                showReplyForm: false,
                                replyAuthor: '',
                                replyText: ''
                            };
                            currentReply = null;
                        } else if (line === '[REPLY]' || line === '[COMPANY_REPLY]') {
                            var isCompany = line === '[COMPANY_REPLY]';
                            currentReply = {
                                id: this.generateId(),
                                author: '',
                                text: '',
                                image: null,
                                likes: 0,
                                timestamp: '',
                                userLiked: false,
                                avatarColor: isCompany ? null : this.getRandomColor(),
                                isCompany: isCompany,
                                avatarLogo: isCompany ? 'img_coment/logo.png' : null,
                                replies: [],
                                showReplyForm: false,
                                replyAuthor: '',
                                replyText: ''
                            };
                            if (currentComment) {
                                currentComment.replies.push(currentReply);
                            }
                        } else if (line.startsWith('author:')) {
                            var target = currentReply || currentComment;
                            if (target) target.author = line.substring(7).trim();
                        } else if (line.startsWith('text:')) {
                            var target = currentReply || currentComment;
                            if (target) target.text = line.substring(5).trim();
                        } else if (line.startsWith('likes:')) {
                            var target = currentReply || currentComment;
                            if (target) target.likes = parseInt(line.substring(6).trim()) || 0;
                        } else if (line.startsWith('timestamp:')) {
                            var target = currentReply || currentComment;
                            if (target) target.timestamp = line.substring(10).trim();
                        } else if (line.startsWith('image:')) {
                            var target = currentReply || currentComment;
                            if (target) {
                                var imagePath = line.substring(6).trim();
                                target.image = imagePath && imagePath !== '' ? imagePath : null;
                            }
                        }
                    }

                    // Добавляем последний комментарий
                    if (currentComment) {
                        comments.push(currentComment);
                    }

                    // Загружаем в allComments
                    this.allComments = comments;
                    this.saveToStorage('allComments', this.allComments);
                    console.log('Загружено комментариев:', comments.length);

                    return comments;
                },

                getDefaultComments: function() {
                    return `[COMMENT]
author: María González
text: Excelente producto, lo recomiendo totalmente.
image:
likes: 45
timestamp: hace 2 días`;
                }
            };
        }
        
        // Оптимизация для Facebook WebView
        document.addEventListener('DOMContentLoaded', () => {
            // Предотвращение зума на double tap
            let lastTouchEnd = 0;
            document.addEventListener('touchend', (event) => {
                const now = (new Date()).getTime();
                if (now - lastTouchEnd <= 300) {
                    event.preventDefault();
                }
                lastTouchEnd = now;
            }, false);
            
            // Улучшение скроллинга
            document.body.style.webkitOverflowScrolling = 'touch';
            
            // Обработка viewport changes
            const viewport = document.querySelector('meta[name="viewport"]');
            
            // Обработчик для форм (предотвращение зума при фокусе)
            const inputs = document.querySelectorAll('input, textarea');
            inputs.forEach(input => {
                input.addEventListener('focus', () => {
                    viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
                });
                
                input.addEventListener('blur', () => {
                    viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
                });
            });
        });
        
        // Кнопка "Cargar más comentarios" удалена - теперь используется Alpine.js
        
        // Плавный скролл для внутренних ссылок
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>

    <!-- Video Player Script -->
    <script>
        const vslPlayerContainer = document.getElementById('vslPlayer');
        const previewVideo = document.getElementById('previewVideo');
        const mainVideoPlayer = document.getElementById('mainVideoPlayer');
        const vslIcon = document.getElementById('vslIcon');

        // HTML-контент для иконки на превью
        const previewIconContent = `
            <div class="play-icon-svg-container">
                <svg class="play-icon-svg" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                    <path d="M8 5v14l11-7z"/>
                </svg>
            </div>
            <span class="play-text-cta">Включите звук, чтобы смотреть</span>
        `;

        // SVG для иконки "стоп/вернуть" на основном видео
        const stopIconSVG = `
            <svg viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                <path d="M16.5 12c0-1.77-1.02-3.29-2.5-4.03v2.21l2.45 2.45c.03-.2.05-.41.05-.63zm2.5 0c0 .94-.2 1.82-.54 2.64l1.51 1.51C20.62 14.91 21 13.5 21 12c0-4.28-2.72-7.79-6.5-8.77v1.51c2.9.86 5 3.54 5 6.76zM1.39 4.22L3 5.83V18c0 1.1.9 2 2 2h4.18l2.97 2.97L13.17 22V12.1l-8.71-8.71L3 2.05 1.39 4.22zM12 4L9.91 6.09 12 8.18V4z"/>
                <path d="M1 1l22 22-1-1z"/>
            </svg>`;

        let isMainVideoPlaying = false;

        function updateIconState() {
            if (isMainVideoPlaying) {
                vslIcon.innerHTML = stopIconSVG;
                vslIcon.className = 'vsl-control-icon main-video-style';
            } else {
                vslIcon.innerHTML = previewIconContent;
                vslIcon.className = 'vsl-control-icon preview-style';
            }
        }

        function switchToMainVideo() {
            if (isMainVideoPlaying) return;

            previewVideo.pause();
            previewVideo.style.display = 'none';

            mainVideoPlayer.style.display = 'block';
            mainVideoPlayer.muted = false;
            mainVideoPlayer.currentTime = 0;

            const playPromise = mainVideoPlayer.play();
            if (playPromise !== undefined) {
                playPromise.then(() => {
                    isMainVideoPlaying = true;
                    updateIconState();
                }).catch(error => {
                    console.error("Ошибка воспроизведения основного видео:", error);
                    isMainVideoPlaying = false;
                    updateIconState();
                    mainVideoPlayer.style.display = 'none';
                    previewVideo.style.display = 'block';
                    previewVideo.currentTime = 0;
                    const previewPlayPromise = previewVideo.play();
                    if (previewPlayPromise !== undefined) {
                        previewPlayPromise.catch(e => console.warn("Не удалось перезапустить превью после ошибки основного:", e));
                    }
                });
            }
        }

        function switchToPreview() {
            if (!isMainVideoPlaying) return;

            mainVideoPlayer.pause();
            mainVideoPlayer.style.display = 'none';

            previewVideo.style.display = 'block';
            previewVideo.muted = true;
            previewVideo.currentTime = 0;

            isMainVideoPlaying = false;
            updateIconState();

            const playPromisePreview = previewVideo.play();
            if (playPromisePreview !== undefined) {
                playPromisePreview.catch(error => {
                    console.warn("Не удалось запустить/перезапустить превью видео при возврате:", error);
                });
            }
        }

        vslPlayerContainer.addEventListener('click', () => {
            if (isMainVideoPlaying) {
                switchToPreview();
            } else {
                switchToMainVideo();
            }
        });

        mainVideoPlayer.addEventListener('ended', () => {
            switchToPreview();
        });

        previewVideo.addEventListener('error', (e) => {
            console.error("Ошибка превью-видео:", previewVideo.error);
        });

        mainVideoPlayer.addEventListener('error', (e) => {
            console.error("Ошибка основного видео:", mainVideoPlayer.error);
             if (isMainVideoPlaying) {
                switchToPreview();
            }
        });

        updateIconState(); // Инициализация начальной иконки
    </script>
</body>
</html>