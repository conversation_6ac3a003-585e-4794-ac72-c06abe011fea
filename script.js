 
        // Alpine.js компонент для комментариев и рейтинга
        function commentsApp() {
            return {
                // Рейтинг
                rating: {
                    userRating: 0,
                    previousUserRating: 0,
                    average: 4.8,
                    total: 3791,
                    breakdown: { 5: 3150, 4: 500, 3: 100, 2: 25, 1: 16 },
                    feedback: '',
                    userHasVoted: false,
                    justUpdated: false,
                    animatingBar: null
                },

                // Комментарии (загружаются через parseCommentsData)
                allComments: [],

                // Пагинация
                commentsPerPage: 10,
                currentPage: 1,

                // Новый комментарий
                newComment: {
                    author: '',
                    text: '',
                    image: null
                },

                // Методы
                init: function() {
                    // Загружаем сохраненные данные
                    const savedRating = this.getFromStorage('userRating');
                    if (savedRating) {
                        this.rating.userRating = savedRating;
                        this.rating.previousUserRating = savedRating;
                        this.rating.userHasVoted = true;
                        this.rating.feedback = `Tu calificación anterior: ${savedRating} ${savedRating === 1 ? 'estrella' : 'estrellas'}. Puedes cambiarla.`;
                    }

                    const savedAuthor = this.getFromStorage('authorName');
                    if (savedAuthor) {
                        this.newComment.author = savedAuthor;
                    }

                    // Загружаем комментарии (включая сохраненные состояния)
                    this.loadTestComments();
                },

                loadSavedStates: function() {
                    var savedComments = this.getFromStorage('allComments');

                    if (savedComments && savedComments.length > 0) {
                        // Применяем сохраненные состояния к загруженным комментариям
                        this.applySavedStates(savedComments);
                    }
                },

                applySavedStates: function(savedComments) {
                    // Создаем карту сохраненных состояний по уникальному ключу
                    var savedStatesMap = {};

                    function mapComments(comments) {
                        for (var i = 0; i < comments.length; i++) {
                            var comment = comments[i];
                            // Используем автора + первые 30 символов текста как уникальный ключ
                            var key = comment.author + '|||' + comment.text.substring(0, 30).replace(/\s+/g, ' ').trim();

                            savedStatesMap[key] = {
                                userLiked: comment.userLiked === true,
                                userDisliked: comment.userDisliked === true,
                                likes: comment.likes || 0,
                                dislikes: comment.dislikes || 0
                            };

                            if (comment.replies && comment.replies.length > 0) {
                                mapComments(comment.replies);
                            }
                        }
                    }

                    mapComments(savedComments);

                    // Применяем состояния к текущим комментариям
                    function applyStates(comments) {
                        for (var i = 0; i < comments.length; i++) {
                            var comment = comments[i];
                            var key = comment.author + '|||' + comment.text.substring(0, 30).replace(/\s+/g, ' ').trim();

                            if (savedStatesMap[key]) {
                                var saved = savedStatesMap[key];
                                comment.userLiked = saved.userLiked;
                                comment.userDisliked = saved.userDisliked;
                                comment.likes = saved.likes;
                                comment.dislikes = saved.dislikes;
                            }

                            if (comment.replies && comment.replies.length > 0) {
                                applyStates(comment.replies);
                            }
                        }
                    }

                    applyStates(this.allComments);
                },

                // Рейтинг методы
                setRating: function(stars) {
                    var self = this;

                    // Если пользователь уже голосовал, показываем удаление старого голоса
                    if (this.rating.userHasVoted && this.rating.previousUserRating > 0) {
                        // Сначала анимируем убирание старого голоса
                        this.rating.animatingBar = this.rating.previousUserRating;
                        this.rating.breakdown[this.rating.previousUserRating]--;
                        this.updateAverageRating();

                        // Через 600ms добавляем новый голос
                        setTimeout(function() {
                            self.addNewRating(stars);
                        }, 600);
                    } else {
                        // Если первый раз голосует, сразу добавляем
                        this.addNewRating(stars);
                    }
                },

                addNewRating: function(stars) {
                    var self = this;

                    // Сохраняем состояние до изменений
                    var wasFirstVote = !this.rating.userHasVoted;

                    // Добавляем новый голос
                    this.rating.breakdown[stars]++;

                    // Увеличиваем total только если это первый голос пользователя
                    if (wasFirstVote) {
                        this.rating.total++;
                    }

                    // Обновляем состояние
                    this.rating.previousUserRating = this.rating.userRating;
                    this.rating.userRating = stars;
                    this.rating.userHasVoted = true;

                    // Пересчитываем средний рейтинг
                    this.updateAverageRating();

                    // Запускаем анимации для новой звезды
                    this.rating.justUpdated = true;
                    this.rating.animatingBar = stars;

                    this.rating.feedback = '¡Gracias por tu calificación!';
                    this.saveToStorage('userRating', stars);

                    // Убираем флаги анимации через время
                    setTimeout(function() {
                        self.rating.justUpdated = false;
                        self.rating.animatingBar = null;
                    }, 1200);
                },

                updateAverageRating: function() {
                    var total = 0;
                    var sum = 0;
                    for (var star in this.rating.breakdown) {
                        var count = this.rating.breakdown[star];
                        total += count;
                        sum += star * count;
                    }
                    this.rating.total = total;
                    this.rating.average = total > 0 ? (sum / total).toFixed(1) : 0;
                },

                getSortedStars: function() {
                    // Сортируем звезды по количеству голосов (больше голосов = выше в списке)
                    var self = this;
                    var keys = Object.keys(this.rating.breakdown);
                    var sorted = keys.sort(function(a, b) {
                        return self.rating.breakdown[b] - self.rating.breakdown[a];
                    });
                    var result = [];
                    for (var i = 0; i < sorted.length; i++) {
                        result.push(Number(sorted[i]));
                    }
                    return result;
                },

                // Комментарии методы
                addComment: function() {
                    if (!this.newComment.author.trim() || !this.newComment.text.trim()) return;

                    var comment = {
                        id: this.generateId(),
                        author: this.newComment.author,
                        text: this.newComment.text,
                        image: this.newComment.image,
                        timestamp: 'Ahora mismo',
                        likes: 0,
                        dislikes: 0,
                        userLiked: false,
                        userDisliked: false,
                        avatarColor: this.getRandomColor(),
                        isCompany: false,
                        replies: [],
                        showReplyForm: false,
                        replyAuthor: '',
                        replyText: ''
                    };

                    this.allComments.unshift(comment);
                    this.saveToStorage('authorName', this.newComment.author);
                    this.saveToStorage('allComments', this.allComments);

                    // Очищаем форму
                    this.newComment.text = '';
                    this.newComment.image = null;
                },

                addReply: function(parentComment) {
                    if (!parentComment.replyAuthor.trim() || !parentComment.replyText.trim()) return;

                    var reply = {
                        id: this.generateId(),
                        author: parentComment.replyAuthor,
                        text: parentComment.replyText,
                        image: null,
                        timestamp: 'Ahora mismo',
                        likes: 0,
                        dislikes: 0,
                        userLiked: false,
                        userDisliked: false,
                        avatarColor: this.getRandomColor(),
                        isCompany: false,
                        replies: [],
                        showReplyForm: false,
                        replyAuthor: '',
                        replyText: ''
                    };

                    // Добавляем ответ в начало списка (вверх)
                    parentComment.replies.unshift(reply);
                    parentComment.showReplyForm = false;
                    parentComment.replyAuthor = '';
                    parentComment.replyText = '';
                    this.saveToStorage('allComments', this.allComments);
                },

                toggleLike: function(comment) {
                    // Если дизлайк активен, убираем его
                    if (comment.userDisliked) {
                        comment.userDisliked = false;
                        comment.dislikes = Math.max(0, comment.dislikes - 1);
                    }

                    comment.userLiked = !comment.userLiked;
                    comment.likes += comment.userLiked ? 1 : -1;
                    comment.likes = Math.max(0, comment.likes);
                    comment.lastLikeTime = new Date().toISOString();

                    this.saveToStorage('allComments', this.allComments);
                },

                toggleDislike: function(comment) {
                    console.log('🔄 Переключаем дизлайк для:', comment.author);

                    // Если лайк активен, убираем его
                    if (comment.userLiked) {
                        comment.userLiked = false;
                        comment.likes = Math.max(0, comment.likes - 1);
                        console.log('❌ Убираем лайк, новое количество:', comment.likes);
                    }

                    comment.userDisliked = !comment.userDisliked;
                    comment.dislikes += comment.userDisliked ? 1 : -1;
                    comment.dislikes = Math.max(0, comment.dislikes); // Не даем уйти в минус
                    comment.lastDislikeTime = new Date().toISOString();

                    console.log('👎 Дизлайк:', comment.userDisliked ? 'добавлен' : 'убран', 'Количество:', comment.dislikes);

                    this.saveToStorage('allComments', this.allComments);
                },

                toggleReplyForm: function(comment) {
                    comment.showReplyForm = !comment.showReplyForm;
                    if (!comment.showReplyForm) {
                        comment.replyAuthor = '';
                        comment.replyText = '';
                    } else {
                        // Автоматически добавляем упоминание автора при открытии формы ответа
                        comment.replyText = '@' + comment.author + ' ';
                        console.log('📝 Добавлено упоминание:', '@' + comment.author + ' ');

                        // Фокусируемся на поле ввода через небольшую задержку
                        var self = this;
                        setTimeout(function() {
                            var textarea = document.querySelector('[data-comment-id="' + comment.id + '"] .reply-textarea');
                            if (textarea) {
                                textarea.focus();
                                // Устанавливаем курсор в конец
                                textarea.setSelectionRange(textarea.value.length, textarea.value.length);
                            }
                        }, 100);
                    }
                },

                getDepth: function(comment) {
                    return 1; // Простая реализация для 2 уровней
                },

                // Методы пагинации
                getVisibleComments: function() {
                    var endIndex = this.currentPage * this.commentsPerPage;
                    return this.allComments.slice(0, endIndex);
                },

                hasMoreComments: function() {
                    return this.allComments.length > (this.currentPage * this.commentsPerPage);
                },

                loadMoreComments: function() {
                    if (this.hasMoreComments()) {
                        this.currentPage++;
                        console.log('📄 Загружаем страницу:', this.currentPage, 'Показываем комментариев:', this.getVisibleComments().length);
                    }
                },

                getRemainingCommentsCount: function() {
                    var remaining = this.allComments.length - (this.currentPage * this.commentsPerPage);
                    return Math.max(0, remaining);
                },

                getInitials: function(name) {
                    if (!name) return '??';
                    var parts = name.trim().split(' ');
                    if (parts.length === 1) return parts[0].substring(0, 2).toUpperCase();
                    return (parts[0][0] + (parts[parts.length - 1][0] || '')).toUpperCase();
                },

                // Обработка упоминаний @имя в тексте
                processText: function(text) {
                    if (!text) return '';

                    // Простой подход: ищем @Имя Фамилия (два слова после @)
                    return text.replace(/@([A-Za-zÀ-ÿ\u00f1\u00d1]+\s+[A-Za-zÀ-ÿ\u00f1\u00d1]+)/g, function(match, name) {
                        var cleanName = name.trim();
                        // Экранируем кавычки для безопасности
                        var escapedName = cleanName.replace(/'/g, "\\'");
                        console.log('🏷️ Найдено упоминание:', match, '→', cleanName);
                        return '<span class="mention-link" onclick="scrollToAuthor(\'' + escapedName + '\')" style="cursor: pointer;">' + match + '</span>';
                    });
                },

                // Прокрутка к комментарию автора
                scrollToAuthor: function(authorName) {
                    // Ищем комментарий этого автора
                    var targetComment = null;
                    for (var i = 0; i < this.allComments.length; i++) {
                        if (this.allComments[i].author === authorName) {
                            targetComment = this.allComments[i];
                            break;
                        }
                        // Проверяем ответы
                        for (var j = 0; j < this.allComments[i].replies.length; j++) {
                            if (this.allComments[i].replies[j].author === authorName) {
                                targetComment = this.allComments[i].replies[j];
                                break;
                            }
                        }
                        if (targetComment) break;
                    }

                    if (targetComment) {
                        // Находим элемент в DOM и прокручиваем к нему
                        var element = document.querySelector('[data-comment-id="' + targetComment.id + '"]');
                        if (element) {
                            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            // Добавляем подсветку
                            element.classList.add('highlighted');
                            setTimeout(function() {
                                element.classList.remove('highlighted');
                            }, 2000);
                        }
                    }
                },

                // Вспомогательные методы
                getRandomColor: function() {
                    // Серенькие цвета с легким оттенком базовых цветов (как в дизайне сайта)
                    var colors = [
                        '#E8E9EA', // Светло-серый (базовый)
                        '#DDD6FE', // Серый с фиолетовым оттенком
                        '#DBEAFE', // Серый с синим оттенком
                        '#D1FAE5', // Серый с зеленым оттенком
                        '#FEE2E2', // Серый с красным оттенком
                        '#FEF3C7', // Серый с желтым оттенком
                        '#E0E7FF', // Серый с индиго оттенком
                        '#F3E8FF'  // Серый с розовым оттенком
                    ];
                    return colors[Math.floor(Math.random() * colors.length)];
                },

                generateId: function() {
                    return Date.now().toString(36) + Math.random().toString(36).substring(2);
                },

                handleImageUpload: function(event) {
                    var file = event.target.files[0];
                    if (file && file.type.indexOf('image/') === 0) {
                        var self = this;
                        var reader = new FileReader();
                        reader.onload = function(e) {
                            self.newComment.image = e.target.result;
                        };
                        reader.readAsDataURL(file);
                    }
                },

                removeImage: function() {
                    this.newComment.image = null;
                },

                // Touch события для мобильных устройств
                handleStarTouch: function(star, event) {
                    // Визуальная обратная связь при touch
                    event.target.style.transform = 'scale(1.1)';
                    var self = this;
                    setTimeout(function() {
                        event.target.style.transform = 'scale(1)';
                    }, 150);
                },

                handleButtonTouch: function(event) {
                    // Визуальная обратная связь для кнопок
                    event.target.style.opacity = '0.7';
                    var self = this;
                    setTimeout(function() {
                        event.target.style.opacity = '1';
                    }, 150);
                },

                // Хранение данных
                saveToStorage: function(key, value) {
                    try {
                        // Пробуем sessionStorage
                        if (typeof sessionStorage !== 'undefined') {
                            sessionStorage.setItem(key, JSON.stringify(value));
                            return;
                        }
                    } catch (e) {
                        // Игнорируем ошибку
                    }

                    try {
                        // Fallback на cookies для WebView
                        var expires = '';
                        var date = new Date();
                        date.setTime(date.getTime() + (1 * 24 * 60 * 60 * 1000)); // 1 день
                        expires = '; expires=' + date.toUTCString();
                        document.cookie = 'fb_' + key + '=' + JSON.stringify(value) + expires + '; path=/';
                    } catch (e2) {
                        console.warn('Storage not available');
                    }
                },

                getFromStorage: function(key) {
                    try {
                        // Пробуем sessionStorage
                        if (typeof sessionStorage !== 'undefined') {
                            var stored = sessionStorage.getItem(key);
                            return stored ? JSON.parse(stored) : null;
                        }
                    } catch (e) {
                        // Игнорируем ошибку
                    }

                    try {
                        // Fallback на cookies
                        var nameEQ = 'fb_' + key + '=';
                        var ca = document.cookie.split(';');
                        for (var i = 0; i < ca.length; i++) {
                            var c = ca[i];
                            while (c.charAt(0) === ' ') c = c.substring(1, c.length);
                            if (c.indexOf(nameEQ) === 0) {
                                return JSON.parse(c.substring(nameEQ.length, c.length));
                            }
                        }
                    } catch (e2) {
                        // Игнорируем ошибку
                    }

                    return null;
                },

                // Загрузка комментариев
                loadTestComments: function() {
                    // Сначала проверяем, есть ли сохраненные комментарии
                    var savedComments = this.getFromStorage('allComments');

                    if (savedComments && savedComments.length > 0) {
                        console.log('📦 Загружаем сохраненные комментарии:', savedComments.length);
                        this.allComments = savedComments;

                        // Проверяем, что у всех комментариев есть нужные поля
                        this.validateComments();
                    } else {
                        console.log('📄 Загружаем комментарии из файла данных');
                        // Загружаем из файла данных только если нет сохраненных
                        var allCommentsData = window.COMMENTS_DATA || this.getDefaultComments();
                        this.parseCommentsData(allCommentsData);
                    }
                },

                validateComments: function() {
                    // Проверяем и дополняем поля комментариев
                    function validateComment(comment) {
                        if (typeof comment.userLiked === 'undefined') comment.userLiked = false;
                        if (typeof comment.userDisliked === 'undefined') comment.userDisliked = false;
                        if (typeof comment.likes === 'undefined') comment.likes = 0;
                        if (typeof comment.dislikes === 'undefined') comment.dislikes = 0;
                        if (!comment.id) comment.id = Date.now().toString(36) + Math.random().toString(36).substring(2);
                        if (!comment.avatarColor) comment.avatarColor = '#E8E9EA';

                        if (comment.replies && comment.replies.length > 0) {
                            for (var i = 0; i < comment.replies.length; i++) {
                                validateComment(comment.replies[i]);
                            }
                        }
                    }

                    for (var i = 0; i < this.allComments.length; i++) {
                        validateComment(this.allComments[i]);
                    }

                    console.log('✅ Комментарии проверены и дополнены');
                },

                parseCommentsData: function(textData) {
                    var comments = [];
                    var lines = textData.split('\n');
                    var currentComment = null;
                    var currentReply = null;

                    for (var i = 0; i < lines.length; i++) {
                        var line = lines[i].trim();

                        // Пропускаем пустые строки и комментарии
                        if (!line || line.startsWith('#')) {
                            continue;
                        }

                        if (line === '[COMMENT]') {
                            // Сохраняем предыдущий комментарий
                            if (currentComment) {
                                comments.push(currentComment);
                            }
                            // Создаем новый комментарий
                            currentComment = {
                                id: this.generateId(),
                                author: '',
                                text: '',
                                image: null,
                                likes: 0,
                                dislikes: 0,
                                timestamp: '',
                                userLiked: false,
                                userDisliked: false,
                                avatarColor: this.getRandomColor(),
                                isCompany: false,
                                replies: [],
                                showReplyForm: false,
                                replyAuthor: '',
                                replyText: ''
                            };
                            currentReply = null;
                        } else if (line === '[REPLY]' || line === '[COMPANY_REPLY]') {
                            var isCompany = line === '[COMPANY_REPLY]';
                            currentReply = {
                                id: this.generateId(),
                                author: '',
                                text: '',
                                image: null,
                                likes: 0,
                                dislikes: 0,
                                timestamp: '',
                                userLiked: false,
                                userDisliked: false,
                                avatarColor: isCompany ? null : this.getRandomColor(),
                                isCompany: isCompany,
                                avatarLogo: isCompany ? 'media/logo.png' : null,
                                replies: [],
                                showReplyForm: false,
                                replyAuthor: '',
                                replyText: ''
                            };
                            if (currentComment) {
                                currentComment.replies.push(currentReply);
                            }
                        } else if (line.startsWith('author:')) {
                            var target = currentReply || currentComment;
                            if (target) target.author = line.substring(7).trim();
                        } else if (line.startsWith('text:')) {
                            var target = currentReply || currentComment;
                            if (target) target.text = line.substring(5).trim();
                        } else if (line.startsWith('likes:')) {
                            var target = currentReply || currentComment;
                            if (target) target.likes = parseInt(line.substring(6).trim()) || 0;
                        } else if (line.startsWith('dislikes:')) {
                            var target = currentReply || currentComment;
                            if (target) target.dislikes = parseInt(line.substring(9).trim()) || 0;
                        } else if (line.startsWith('timestamp:')) {
                            var target = currentReply || currentComment;
                            if (target) target.timestamp = line.substring(10).trim();
                        } else if (line.startsWith('image:')) {
                            var target = currentReply || currentComment;
                            if (target) {
                                var imagePath = line.substring(6).trim();
                                target.image = imagePath && imagePath !== '' ? imagePath : null;
                            }
                        }
                    }

                    // Добавляем последний комментарий
                    if (currentComment) {
                        comments.push(currentComment);
                    }

                    // Загружаем в allComments
                    this.allComments = comments;
                    this.saveToStorage('allComments', this.allComments);
                    console.log('Загружено комментариев:', comments.length);

                    return comments;
                },

                getDefaultComments: function() {
                    return `[COMMENT]
author: María González
text: Excelente producto, lo recomiendo totalmente.
image:
likes: 45
timestamp: hace 2 días`;
                }
            };
        }

        // Глобальная функция для прокрутки к автору (вызывается из HTML)
        function scrollToAuthor(authorName) {
            console.log('🔍 Ищем автора:', authorName);

            // Ждем немного, чтобы DOM успел обновиться
            setTimeout(function() {
                // Ищем элемент с автором напрямую в DOM
                var allComments = document.querySelectorAll('[data-comment-id]');
                var targetElement = null;

                console.log('📋 Всего комментариев найдено:', allComments.length);

                for (var i = 0; i < allComments.length; i++) {
                    var authorElement = allComments[i].querySelector('.comment-author');
                    if (authorElement) {
                        var authorText = authorElement.textContent.trim();
                        console.log('👤 Проверяем автора:', authorText);

                        if (authorText === authorName) {
                            targetElement = allComments[i];
                            console.log('✅ Найден автор!');
                            break;
                        }
                    }
                }

                if (targetElement) {
                    console.log('🎯 Прокручиваем к элементу');
                    targetElement.scrollIntoView({ behavior: 'smooth', block: 'center' });

                    // Подсвечиваем только имя автора
                    var authorElement = targetElement.querySelector('.comment-author');
                    if (authorElement) {
                        authorElement.classList.add('author-highlighted');
                        setTimeout(function() {
                            authorElement.classList.remove('author-highlighted');
                        }, 2000);
                    }
                } else {
                    console.log('❌ Автор не найден:', authorName);
                    console.log('📝 Доступные авторы:');
                    var authors = document.querySelectorAll('.comment-author');
                    for (var j = 0; j < authors.length; j++) {
                        console.log('  -', authors[j].textContent.trim());
                    }
                }
            }, 100);
        }

        // Оптимизация для Facebook WebView
        document.addEventListener('DOMContentLoaded', () => {
            // Предотвращение зума на double tap
            let lastTouchEnd = 0;
            document.addEventListener('touchend', (event) => {
                const now = (new Date()).getTime();
                if (now - lastTouchEnd <= 300) {
                    event.preventDefault();
                }
                lastTouchEnd = now;
            }, false);
            
            // Улучшение скроллинга
            document.body.style.webkitOverflowScrolling = 'touch';
            
            // Обработка viewport changes
            const viewport = document.querySelector('meta[name="viewport"]');
            
            // Обработчик для форм (предотвращение зума при фокусе)
            const inputs = document.querySelectorAll('input, textarea');
            inputs.forEach(input => {
                input.addEventListener('focus', () => {
                    viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
                });
                
                input.addEventListener('blur', () => {
                    viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
                });
            });
        });
        
        // Кнопка "Cargar más comentarios" удалена - теперь используется Alpine.js
        
        // Плавный скролл для внутренних ссылок
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

