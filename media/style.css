
:root {
    /* Colors */
    --color-background: rgb(248, 249, 250);         /* Светлый фон для body */
    --color-surface: rgb(255, 255, 255);            /* Белый фон для основных контейнеров контента */
    --color-text-primary: rgb(33, 37, 41);          /* Темно-серый для основного текста (высокий контраст) */
    --color-text-secondary: rgb(85, 85, 85);        /* Серый для мета-информации, подписей */
    --color-text-subtle: rgb(119, 119, 119);        /* Еще более светлый серый для менее важных деталей */
    
    --color-accent-primary: rgb(0, 114, 198);       /* Спокойный, но уверенный синий (ссылки, кнопки) */
    --color-accent-primary-hover: rgb(0, 90, 158);  /* Темнее для hover */
    --color-accent-primary-rgb: 0, 114, 198;        /* Для box-shadow с rgba */
    
    --color-accent-secondary: rgb(40, 167, 69);     /* Зеленый (например, для категорий или позитивных сообщений) */
    --color-accent-secondary-hover: rgb(30, 126, 52);/* Темнее для hover */
    
    --color-border: rgb(224, 224, 224);             /* Светло-серый для границ */
    --color-border-subtle: rgb(238, 238, 238);      /* Еще светлее для мягких разделителей */

    --color-header-bg: rgb(0, 114, 198);
    --color-header-text: rgb(255, 255, 255);

    --color-warning-bg: rgb(255, 248, 225);         /* Светло-желтый фон для предупреждений */
    --color-warning-text: rgb(141, 110, 99);        /* Коричневатый текст для предупреждений (хороший контраст) */
    --color-warning-border: rgb(255, 209, 128);     /* Оранжево-желтая граница */

    --color-info-bg: rgb(227, 242, 253);            /* Светло-голубой фон для информации */
    --color-info-text: rgb(13, 71, 161);            /* Темно-синий текст для информации (хороший контраст) */
    --color-info-border: rgb(144, 202, 249);        /* Голубая граница */

    --color-highlight-yellow: rgb(255, 193, 7);     /* Для звезд рейтинга */
    --color-tag-bg: rgb(241, 243, 245);             /* Цвет фона для тегов */
    --color-tag-text: var(--color-text-secondary);
    --color-tag-border: var(--color-border);

    /* Typography */
    --font-family-base: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
    --font-size-base-html: 19px;        /* Устанавливается на html для расчета rem */
    
    --font-size-body: 1rem;             /* = 19px */
    --font-size-sm: 0.875rem;           /* ~16.6px */
    --font-size-xs: 0.75rem;            /* ~14.25px */
    --font-size-lg: 1.125rem;           /* ~21.3px */
    
    --font-size-h1: 1.8rem;             /* ~34px */
    --font-size-h2: 1.5rem;             /* ~28.5px */
    --font-size-h3: 1.25rem;            /* ~23.75px */
    --font-size-h4: 1.1rem;             /* ~21px */

    --line-height-base: 1.7;
    --line-height-heading: 1.3;
    --line-height-tight: 1.5;

     --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;

    /* Spacing */
    --space-xxs: 0.25rem; /* ~4.75px */
    --space-xs:  0.5rem;  /* ~9.5px */
    --space-sm:  0.75rem; /* ~14.25px */
    --space-md:  1rem;    /* 19px */
    --space-lg:  1.5rem;  /* ~28.5px */
    --space-xl:  2rem;    /* 38px */
    --space-xxl: 3rem;    /* 57px */
    
    /* Borders & Shadows */
    --border-radius-sm: 4px;
    --border-radius-base: 8px;
    --border-radius-lg: 12px;
    --border-radius-pill: 9999px;
    --box-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.08);
    --box-shadow-lg: 0 4px 12px rgba(0, 0, 0, 0.1);
    --box-shadow-focus: 0 0 0 0.2rem rgba(var(--color-accent-primary-rgb), 0.25);

    /* Transitions */
    --transition-base: all 0.2s ease-in-out;

    /* Touch targets */
    --touch-target-min-size: 44px; /* Минимальный размер для интерактивных элементов */
}

/* ==========================================================================
   2. Base Styles & Resets
   ========================================================================== */
*,
*::before,
*::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: var(--font-size-base-html); 
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch; /* Для плавной прокрутки в WebView на iOS */
}

body {
    font-family: var(--font-family-base);
    font-size: var(--font-size-body);
    line-height: var(--line-height-base);
    color: var(--color-text-primary);
    background-color: var(--color-background);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden;
}

/* ==========================================================================
   3. Typography - Default styles for text elements
   ========================================================================== */
p {
    margin-bottom: var(--space-md);
}
p:last-child { margin-bottom: 0; }

h1, h2, h3, h4 {
    margin-top: var(--space-lg);
    margin-bottom: var(--space-sm);
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-heading);
}
h1 { font-size: var(--font-size-h1); }
h2 { font-size: var(--font-size-h2); }
h3 { font-size: var(--font-size-h3); }
h4 { font-size: var(--font-size-h4); font-weight: var(--font-weight-semibold); }

a {
    color: var(--color-accent-primary);
    text-decoration: underline;
    text-decoration-thickness: 1.5px; /* Чуть толще для заметности */
    text-underline-offset: 0.2em;  
    transition: var(--transition-base);
}
a:hover, a:focus {
    color: var(--color-accent-primary-hover);
    text-decoration-thickness: 2px;
}
a:focus { outline: 2px solid var(--color-accent-primary); outline-offset: 2px; } /* Для доступности */


strong, b { font-weight: var(--font-weight-bold); }
em, i { font-style: italic; }

ul, ol {
    margin-top: var(--space-sm);
    margin-bottom: var(--space-md);
    padding-left: var(--space-lg);
}
li { margin-bottom: var(--space-xs); }

img, video {
    max-width: 100%;
    height: auto;
    display: block;
}

hr {
    border: 0;
    border-top: 1px solid var(--color-border-subtle);
    margin: var(--space-lg) 0;
}

/* ==========================================================================
   4. Layout & Components
   ========================================================================== */

/* Main page container (optional, if you wrap everything) */
.container {
    max-width: 100%;
    margin: 0 auto;
    background-color: var(--color-surface);
    min-height: 100vh;
}

/* Header */
.go-order{
    font-size: var(--font-size-h3); 
    background-color: var(--color-accent-secondary);
    font-weight: var(--font-weight-semibold);
    text-align: center;
    color: var(--color-header-text);
    padding: 1rem 0;
   position: fixed;
   margin: 0 1rem 0.5rem 1rem;
   border-radius: 0.5rem;
   text-decoration: none;
       border-bottom: 3px solid var( --color-accent-secondary-hover);
           box-shadow: 0 4px 11px 0 rgba(0, 0, 0, 0.25);
bottom: 0;
left: 0;
right: 0;
z-index: 1000;
}
.go-order:hover,
.go-order:focus,
.go-order:active,
.go-order:visited {
    color: var(--color-header-text) !important;
    background-color: var(--color-accent-secondary-hover);
    text-decoration: none;
    -webkit-text-fill-color: var(--color-header-text); /* iOS Safari fix */
    outline: none; /* Убираем стандартную обводку */
}

/* Article specific Styles */
.article-header {
    padding: var(--space-lg) var(--space-md);
    border-bottom: 1px solid var(--color-border-subtle);
}
.article-header p{
    font-size: var(--font-size-lg);
}
.article-header p span {
    background-color: var(--color-warning-border);
    padding: 0 0.2em;
}
.article-category {
    background-color: var(--color-border);
    color: var(--color-text-subtle);
    padding: var(--space-xxs) var(--space-xs);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-semibold);
    text-transform: uppercase;
    border-radius: var(--border-radius-sm);
    margin-bottom: var(--space-sm);
display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}
.article-category.light-label{
      background-color: var(--color-info-bg);
    color: var(--color-info-text);
 border: 1px solid rgb(14,71,162, 0.25);

}
.article-category.accent-label{
       background-color: var(--color-info-text);
    color: var(--color-header-text);
  border: 1px solid rgb(0,0,0, 0.25);
}
.article-title { /* Assuming this is an H1 */
    margin-top: 0; /* Already styled by H1 defaults */
    margin-bottom: var(--space-md);
}
.article-title span{ /* Assuming this is an H1 */
    font-weight: var(--font-weight-light);
}
/* --- Модифицированные и новые стили для .article-meta --- */
.article-meta {
    display: flex;
    align-items: flex-start; /* Элементы выравниваются по верху */
    justify-content: space-between;
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    margin-bottom: var(--space-lg); /* Немного увеличим отступ снизу */
    flex-wrap: wrap; 
    gap: var(--space-md); 
}

.author-info {
    display: flex;
    align-items: flex-start;
    gap: var(--space-sm);
    flex-basis: 60%; 
    min-width: 280px; 
    margin-bottom:var(--space-md);
}

.author-avatar {
    width: 52px; 
    height: 52px;
    border-radius: 50%;
    object-fit: cover;
    flex-shrink: 0;
    background: var( --color-info-bg);

}

.author-details {
    display: flex;
    flex-direction: column;
    gap: var(--space-xxs); /* Увеличим расстояние между элементами */
}

.author-name {
    display: flex;
    align-items: center;
    gap: var(--space-xxs);
    font-weight: var(--font-weight-semibold);
    color: var(--color-text-primary);
    font-size: var(--font-size-body); /* Имя автора может быть немного крупнее */
    line-height: 1.3;
}

.verified-badge {
    width: 1.1em; /* Размер относительно font-size .author-name */
    height: 1.1em;
    fill: var(--color-accent-primary); /* Основной цвет галочки */
    stroke: var(--color-surface); /* Обводка цветом фона контейнера */
    stroke-width: 1.5;
    flex-shrink: 0;
}

.meta-item {
    display: flex;
    align-items: flex-start; /* Иконка по верху текста */
    gap: var(--space-xs);
    color: var(--color-text-secondary);
    line-height: var(--line-height-tight);
}

.meta-icon { /* Обновляем существующий класс */
    width: 18px; 
    height: 18px;
    fill: none; /* Для stroke-based Lucide icons */
    stroke: currentColor; 
    stroke-width: 2;
    flex-shrink: 0;
    margin-top: 0.15em; /* Тонкая подстройка для визуального выравнивания с первой строкой текста */
}
.rating-star-icon { /* Для иконки звезды в рейтинге, если нужен особый цвет */
    stroke: var(--color-highlight-yellow); /* Или fill, если иконка с заливкой */
    fill: var(--color-highlight-yellow); /* Для залитой звезды */
}


.meta-text-block {
    display: flex;
    flex-direction: column;
}

.meta-text-block span:first-child {
    font-weight: var(--font-weight-medium);
    color: var(--color-text-primary); /* Основное значение чуть темнее */
    font-size: var(--font-size-sm); /* Немного уменьшим для компактности */
}

.meta-text-block span:last-child {
    font-size: var(--font-size-xs);
    color: var(--color-text-subtle);
}

.rating-value-text { /* Отдельный класс для значения рейтинга */
    color: var(--color-highlight-yellow) !important; /* Важно, чтобы перебить общий цвет */
    font-weight: var(--font-weight-semibold);
}


.additional-meta {
    display: flex;
    flex-direction: column; 
    align-items: flex-start; /* Элементы в этой колонке выравниваются по своему левому краю */
    gap: var(--space-sm);  /* Расстояние между "комментариями" и "просмотрами" */
    flex-basis: 35%; /* Оставшееся место */
    min-width: 140px; /* Минимальная ширина для правой колонки */
    padding-top: var(--space-xxs); /* Небольшой отступ сверху для визуальной гармонии */
}

/* Удаление или адаптация старых стилей, если они больше не нужны в этой конфигурации */
.meta-left, .meta-right {
    /* Если эти классы использовались только для старой структуры .article-meta, */
    /* их можно закомментировать или удалить */
     display: none; /* Скрываем, если они больше не нужны в этой части */
}

.image-caption {
    padding: var(--space-xs) var(--space-md);
    font-size: var(--font-size-xs);
    color: var(--color-text-subtle);
    font-style: italic;
    background-color: var(--color-background);
    border-bottom: 1px solid var(--color-border-subtle);
    text-align: center;
}
.image-caption img{
      display: block;
   margin: 10px auto 0;
}

.article-body {
    padding: 0  var(--space-md) var(--space-lg);
}
/* p, h2, h3, ul, ol, li inside .article-body will use default styles or can be overridden slightly */
.article-body h2 { margin-top: var(--space-xl); }
.article-body h3 { margin-top: var(--space-lg); }
.article-body ul, .article-body ol { margin-left: var(--space-md); /* Indent lists slightly more if needed */ }


/* Blockquotes & Info Boxes */
.quote-highlight {
    background-color: var(--color-info-bg);
    border-left: 4px solid var(--color-accent-primary);
    padding: var(--space-md) var(--space-lg);
    margin: var(--space-lg) 0;
    font-size: var(--font-size-lg);
    font-style: italic;
    color: var(--color-info-text);
    border-radius: 0 var(--border-radius-base) var(--border-radius-base) 0;
}
.quote-author {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--color-text-secondary);
    margin-top: var(--space-sm);
    font-style: normal;
    text-align: right;
}

.info-box, .warning-box {
    border-radius: var(--border-radius-base);
    padding: var(--space-md);
    margin: var(--space-lg) 0;
    border-width: 1px;
    border-style: solid;
}
.info-box {
    background-color: var(--color-info-bg);
    border-color: var(--color-info-border);
    color: var(--color-info-text);
}
.warning-box {
    background-color: var(--color-warning-bg);
    border-color: var(--color-warning-border);
    color: var(--color-warning-text);
}
.info-box-title { /* Common title for these boxes */
    font-weight: var(--font-weight-semibold);
    color: inherit;
    margin-bottom: var(--space-xs);
    font-size: var(--font-size-body);
}

/* Tags */
.article-tags {
    padding: var(--space-md);
    background-color: var(--color-background);
    border-top: 1px solid var(--color-border);
    border-bottom: 1px solid var(--color-border);
    margin-top: var(--space-lg);
}
.tags-title {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--color-text-secondary);
    margin-bottom: var(--space-sm);
    text-transform: uppercase;
}
.tag {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: var(--color-tag-bg);
    color: var(--color-tag-text);
    padding: var(--space-xs) var(--space-sm);
    margin: var(--space-xxs);
    border-radius: var(--border-radius-pill);
    font-size: var(--font-size-sm);
    text-decoration: none;
    border: 1px solid var(--color-tag-border);
    transition: var(--transition-base);
    min-height: calc(var(--touch-target-min-size) - 8px); /* slightly less for tags if too bulky */
    line-height: var(--line-height-tight);
}
.tag:hover, .tag:focus, .tag:active {
    background-color: var(--color-accent-primary);
    color: var(--color-header-text);
    border-color: var(--color-accent-primary);
}
.tag:focus { box-shadow: var(--box-shadow-focus); }


/* Video Player */
  .vsl-player-container {
            position: relative;
            width: 100%;
            max-width: 720px;
            aspect-ratio: 1 / 1;
            background-color: #000;
            overflow: hidden;
            cursor: pointer;
            margin: 0 auto;
        }

        .vsl-player-container video {
            display: block;
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        #mainVideoPlayer {
            display: none;
        }

        /* --- Стили для иконок --- */
        .vsl-control-icon {
            position: absolute;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10;
            transition: opacity 0.2s ease,  transform 0.2s ease;
            pointer-events: none;
        }

        .vsl-control-icon svg {
            fill: white;
        }

        /* Стиль иконки для ПРЕВЬЮ (большая, по центру, с текстом и анимацией) */
        .vsl-control-icon.preview-style {
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            flex-direction: column; 
            text-align: center;
            opacity: 1;
            background-color: transparent;
        }

        .vsl-control-icon.preview-style .play-icon-svg-container {
            width: 3rem; 
            height: 3rem; 
            background-color: rgb(0, 0, 0);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 12px;
            animation: pulse-animation 2s infinite ease-in-out; /* Анимация пульсации */
        }

        .vsl-control-icon.preview-style .play-icon-svg {
            width: 1.5rem; 
            height: 1.5rem; 
        }

        .vsl-control-icon.preview-style .play-text-cta {
            color: white;
            font-size: 16px;
            font-weight: 500;
            line-height: 1.4;
            text-shadow: 0 1px 3px rgba(0,0,0,0.5);
        }
        
  


        /* Стиль иконки для ОСНОВНОГО ВИДЕО (маленькая, в углу) */
        .vsl-control-icon.main-video-style {
            left: 15px;
            bottom: 15px;
            width: 40px;
            height: 40px;
            background-color: rgba(20, 20, 20, 0.6); /* Темнее фон для лучшего контраста */
            border-radius: 6px;
            opacity: 0; /* Скрыта по умолчанию, появляется при наведении на контейнер */
        }
        .vsl-control-icon.main-video-style svg {
            width: 20px;
            height: 20px;
        }
        .vsl-player-container:hover .vsl-control-icon.main-video-style {
            opacity: 1;
            background-color: rgba(0, 0, 0, 0.75);
        }

        /* Анимация пульсации */
        @keyframes pulse-animation {
            0% { transform: scale(1); box-shadow: 0 2px 8px rgba(0,0,0,0.3); }
            50% { transform: scale(1.1); box-shadow: 0 4px 15px rgba(0,0,0,0.4); }
            100% { transform: scale(1); box-shadow: 0 2px 8px rgba(0,0,0,0.3); }
        }

/* Rating Section */
.rating-section {
    padding: var(--space-lg) var(--space-md);
    background-color: var(--color-surface);
    border-bottom: 1px solid var(--color-border);
}
.rating-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--space-md);
}
.rating-container {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    margin-bottom: var(--space-md);
}
.stars { display: flex; gap: var(--space-xxs); }
.star {
    width: 28px; height: 28px;
    cursor: pointer;
    font-size: 1.6rem; /* Adjust if using font icons */
    color: #DDDDDD;
    transition: var(--transition-base);
    line-height: 1;
    /* For SVG icons ensure they scale well */
}
.star.active, .star:hover, .star:focus { color: var(--color-highlight-yellow); }
.star:focus { outline: 2px solid var(--color-highlight-yellow); outline-offset: 1px; }

.rating-stats { font-size: var(--font-size-sm); color: var(--color-text-secondary); }
.rating-bars { margin-top: var(--space-md); }
.rating-bar {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    margin-bottom: var(--space-xs);
    font-size: var(--font-size-sm);
}
.bar-label { width: 20px; color: var(--color-text-secondary); text-align: right; }
.bar-fill {
    flex: 1;
    height: 10px;
    background-color: var(--color-tag-bg);
    border-radius: var(--border-radius-sm);
    overflow: hidden;
}
.bar-progress {
    height: 100%;
    background-color: var(--color-highlight-yellow);
    transition: width 0.4s ease-in-out;
}
.bar-count {
    width: 35px;
    text-align: right;
    color: var(--color-text-secondary);
    font-size: var(--font-size-xs);
}

/* Comments Section */
.comments-section { background-color: var(--color-surface); }
.comments-header {
    padding: var(--space-lg) var(--space-md) var(--space-md) var(--space-md);
    border-bottom: 1px solid var(--color-border);
    background-color: var(--color-background);
}
.comments-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--space-xs);
}
.comments-count { font-size: var(--font-size-sm); color: var(--color-text-secondary); }

.comment-form {
    padding: var(--space-md);
    background-color: var(--color-surface);
    border-bottom: 2px solid var(--color-border);
}
.comment-textarea {
    width: 100%;
    min-height: 100px;
    padding: var(--space-sm);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius-base);
    font-size: var(--font-size-body);
    font-family: inherit;
    line-height: var(--line-height-base);
    resize: vertical;
    margin-bottom: var(--space-sm);
}
.comment-textarea::placeholder { color: var(--color-text-subtle); }
.comment-textarea:focus {
    outline: none;
    border-color: var(--color-accent-primary);
    box-shadow: var(--box-shadow-focus);
}

/* General Button Styles (Applied to .comment-submit, .load-more, and can be a class .button) */
.button, .comment-submit, .load-more {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-sm) var(--space-lg);
    font-size: var(--font-size-body);
    font-weight: var(--font-weight-semibold);
    border-radius: var(--border-radius-base);
    cursor: pointer;
    transition: var(--transition-base);
    min-height: var(--touch-target-min-size);
    text-decoration: none; /* For <a> styled as buttons */
    border: 1px solid transparent; /* Base border for consistency */
}
.button:focus, .comment-submit:focus, .load-more:focus {
    outline: none;
    box-shadow: var(--box-shadow-focus);
}

/* Primary Button Style (e.g., .comment-submit) */
.button-primary, .comment-submit {
    background-color: var(--color-accent-primary);
    color: var(--color-header-text);
    border-color: var(--color-accent-primary);
}
.button-primary:hover, .comment-submit:hover,
.button-primary:active, .comment-submit:active {
    background-color: var(--color-accent-primary-hover);
    border-color: var(--color-accent-primary-hover);
}

/* Secondary/Outline Button Style (e.g., .load-more) */
.button-outline, .load-more {
    background-color: var(--color-surface);
    color: var(--color-accent-primary);
    border-color: var(--color-accent-primary);
}
.button-outline:hover, .load-more:hover,
.button-outline:active, .load-more:active {
    background-color: var(--color-accent-primary);
    color: var(--color-header-text);
}
.load-more { /* Specific overrides if .button-outline is not enough */
    width: calc(100% - 2 * var(--space-md)); 
    margin: var(--space-lg) auto; 
}


.comments-list { /* No padding, .comment handles it */ }
.comment {
    padding: var(--space-md);
    border-bottom: 1px solid var(--color-border-subtle);
}
.comment:last-child { border-bottom: none; }
.comment-header {
    display: flex;
    align-items: flex-start;
    gap: var(--space-sm);
    margin-bottom: var(--space-xs);
}
.comment-avatar {
    width: 40px; height: 40px;
    border-radius: 50%;
    background-color: var(--color-tag-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: var(--font-weight-semibold);
    color: var(--color-text-secondary);
    flex-shrink: 0;
}
.comment-meta { flex: 1; }
.comment-author {
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-sm);
    margin-bottom: var(--space-xxs);
}
.comment-time { font-size: var(--font-size-xs); color: var(--color-text-subtle); }
.comment-text {
    /* Uses default p styles, line-height from body */
    color: var(--color-text-primary);
    margin-bottom: var(--space-sm);
}
.comment-text p { margin-bottom: var(--space-xs); } /* Tighter paragraphs within a comment */

.comment-actions {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-xs) var(--space-md);
    margin-top: var(--space-sm);
}
.comment-action { /* Text button / link-like action */
    display: inline-flex;
    align-items: center;
    gap: var(--space-xxs);
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    background-color: transparent;
    border: none;
    cursor: pointer;
    padding: var(--space-xs); /* Generous touch area */
    border-radius: var(--border-radius-sm);
    min-height: calc(var(--touch-target-min-size) - 8px);
    transition: var(--transition-base);
}
.action-icon {
    width: 1.2em; height: 1.2em;
    fill: currentColor;
    flex-shrink: 0;
}
.comment-action:hover, .comment-action:focus, .comment-action.liked, .comment-action:active {
    color: var(--color-accent-primary);
    background-color: rgba(var(--color-accent-primary-rgb), 0.08); /* Subtle background */
}
.comment-action:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(var(--color-accent-primary-rgb), 0.2);
}

.comment-replies {
    margin-top: var(--space-sm);
    margin-left: + var(--space-sm); /* Avatar width + gap */
    padding-left: var(--space-xl);
    border-left: 2px solid var(--color-border);
}
/* .reply (if it's a .comment nested) will inherit .comment styles */

/* Alpine.js интеграция - дополнительные стили */

/* Стили для SVG иконок */
.star-icon {
    width: 24px;
    height: 24px;
    filter: brightness(0) saturate(100%) invert(75%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(85%) contrast(95%);
    transition: filter 0.1s ease-in-out;
}

.star.active .star-icon {
    filter: brightness(0) saturate(100%) invert(85%) sepia(100%) saturate(2000%) hue-rotate(315deg) brightness(100%) contrast(100%);
}

.like-icon {
    width: 16px;
    height: 16px;
    margin-right: 6px;
    filter: brightness(0) saturate(100%) invert(45%) sepia(10%) saturate(500%) hue-rotate(200deg) brightness(95%) contrast(85%);
    transition: filter 0.1s ease-in-out;
}

.comment-action.liked .like-icon {
    filter: brightness(0) saturate(100%) invert(27%) sepia(100%) saturate(7000%) hue-rotate(220deg) brightness(100%) contrast(100%);
}

.upload-icon {
    width: 18px;
    height: 18px;
    margin-right: 8px;
    filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%);
}

/* Стили для формы комментариев */
.comment-form-main {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 12px;
    border: 1px solid #e4e6ea;
}

.comment-name-input {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--color-border);
    border-radius: 8px;
    font-size: 16px; /* Важно для мобильных */
    font-family: inherit;
    background: white;
    outline: none;
    margin-bottom: 10px;
    -webkit-appearance: none;
    appearance: none;
}

.comment-name-input:focus {
    border-color: var(--color-accent-primary);
}

.comment-photo-btn {
    background: var(--color-accent-secondary);
    color: white;
    border: none;
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 10px;
    -webkit-tap-highlight-color: transparent;
    -webkit-appearance: none;
    appearance: none;
    font-family: inherit;
}

.comment-photo-btn:active {
    background: var(--color-accent-secondary-hover);
}

.comment-image-preview {
    margin-bottom: 10px;
    position: relative;
}

.comment-image-preview img {
    max-width: 200px;
    max-height: 200px;
    border-radius: 8px;
    border: 1px solid var(--color-border);
}

.comment-remove-image {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #ff4444;
    color: white;
    border: none;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    -webkit-tap-highlight-color: transparent;
}

.comment-remove-image:active {
    background: #cc0000;
}

/* Стили для формы ответов */
.comment-reply-form-container {
    margin-top: 10px;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 12px;
    border: 1px solid var(--color-border);
}

.reply-name-input {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--color-border);
    border-radius: 8px;
    font-size: 16px; /* Важно для мобильных */
    font-family: inherit;
    background: white;
    outline: none;
    margin-bottom: 10px;
    -webkit-appearance: none;
    appearance: none;
}

.reply-name-input:focus {
    border-color: var(--color-accent-primary);
}

.reply-textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--color-border);
    border-radius: 8px;
    font-size: 16px; /* Важно для мобильных */
    font-family: inherit;
    background: white;
    outline: none;
    resize: none;
    min-height: 60px;
    margin-bottom: 10px;
    -webkit-appearance: none;
    appearance: none;
}

.reply-textarea:focus {
    border-color: var(--color-accent-primary);
}

/* Анимации для рейтинга */
.rating-feedback {
    font-weight: 500;
    color: #bcc0c4; /* Серый по умолчанию */
    opacity: 0.7;
    transition: all 0.3s ease-in-out;
}

.rating-feedback.visible {
    color: var(--color-accent-primary); /* Синий когда активно */
    opacity: 1;
    transform: scale(1.05);
}

.bar-progress.animating::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent);
    animation: shimmer 1.2s ease-in-out;
}

.bar-progress.animating {
    animation: barGlow 1.2s ease-in-out;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

@keyframes barGlow {
    0% {
        background: var(--color-highlight-yellow);
        box-shadow: none;
    }
    50% {
        background: #FF6B35;
        box-shadow: 0 0 20px rgba(255, 215, 0, 0.8);
        transform: scaleY(1.1);
    }
    100% {
        background: var(--color-highlight-yellow);
        box-shadow: none;
        transform: scaleY(1);
    }
}

.bar-count.updated {
    color: var(--color-accent-primary);
    transform: scale(1.2);
    animation: countPulse 0.6s ease-in-out;
}

@keyframes countPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.3); color: var(--color-highlight-yellow); }
    100% { transform: scale(1.2); color: var(--color-accent-primary); }
}
