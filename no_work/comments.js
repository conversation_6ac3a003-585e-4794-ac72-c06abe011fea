// Полифилл для Element.closest() для старых WebView
if (!Element.prototype.closest) {
    Element.prototype.closest = function(s) {
        var el = this;
        do {
            if (el.matches && el.matches(s)) return el;
            el = el.parentElement || el.parentNode;
        } while (el !== null && el.nodeType === 1);
        return null;
    };
}

// Полифилл для Element.matches() для старых WebView
if (!Element.prototype.matches) {
    Element.prototype.matches = Element.prototype.matchesSelector ||
        Element.prototype.webkitMatchesSelector ||
        Element.prototype.mozMatchesSelector ||
        Element.prototype.msMatchesSelector;
}

document.addEventListener('DOMContentLoaded', function() {
    const commentsList = document.getElementById('fb-style-comments-list');
    const mainCommentForm = document.getElementById('fb-style-comments-main-comment-form');
    const authorNameInput = document.getElementById('fb-style-comments-author-name');
    const authorTextInput = document.getElementById('fb-style-comments-author-text');
    const imageUploadInput = document.getElementById('fb-style-comments-image-upload');
    const imageUploadBtn = document.getElementById('fb-style-comments-image-btn');
    const imagePreview = document.getElementById('fb-style-comments-image-preview');
    const previewImg = document.getElementById('fb-style-comments-preview-img');
    const removeImageBtn = document.getElementById('fb-style-comments-remove-image');

    const COOKIE_NAME_COMMENTS = 'fbUserComments_v3_final_b'; // Новое имя для чистоты
    const LS_KEY_RATING = 'fbUserProductRating_v3_final_b';
    const LS_KEY_AUTHOR_NAME = 'fbUserCommentAuthorName_v3_final_b';
    const MAX_REPLY_DEPTH = 2; // ИЗМЕНЕНО: Позволяет Основной -> Ответ -> Ответ на ответ

    // --- Rating Data and Elements ---
    const ratingSection = document.querySelector('.fb-style-comments-rating-section');
    let productRatingData = {
        votesByStar: { 5: 3150, 4: 500, 3: 100, 2: 25, 1: 16 },
        totalVotes: 3791,
        sumOfRatings: 0,
        userVotedInSession: false,
        currentUserRating: null
    };
    // Заменяем Object.entries() на совместимый код для старых WebView
    productRatingData.sumOfRatings = 0;
    for (var star in productRatingData.votesByStar) {
        if (productRatingData.votesByStar.hasOwnProperty(star)) {
            productRatingData.sumOfRatings += parseInt(star, 10) * productRatingData.votesByStar[star];
        }
    }

    const avgRatingSpan = ratingSection ? ratingSection.querySelector('.fb-style-comments-avg-rating') : null;
    const totalVotesSpan = ratingSection ? ratingSection.querySelector('.fb-style-comments-total-votes') : null;
    const ratingBarElements = ratingSection ? ratingSection.querySelectorAll('.fb-style-comments-rating-bar') : [];
    const starsContainer = ratingSection ? ratingSection.querySelector('.fb-style-comments-stars') : null;
    // Заменяем Array.from() на совместимый код для старых WebView
    var stars = [];
    if (starsContainer) {
        var starNodeList = starsContainer.querySelectorAll('.fb-style-comments-star');
        for (var i = 0; i < starNodeList.length; i++) {
            stars.push(starNodeList[i]);
        }
    }
    const userRatingFeedbackEl = ratingSection ? ratingSection.querySelector('.fb-style-comments-user-rating-feedback') : null;

    // --- Cookie Functions ---
    function setCookie(name, value, days) {
        let expires = "";
        if (days) {
            const date = new Date();
            date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
            expires = "; expires=" + date.toUTCString();
        }
        try {
            // Убираем SameSite и Secure для совместимости с Facebook WebView
            document.cookie = name + "=" + (JSON.stringify(value) || "") + expires + "; path=/";
        } catch (e) {
            console.error("Error setting cookie:", e);
        }
    }

    function getCookie(name) {
        const nameEQ = name + "=";
        const ca = document.cookie.split(';');
        for (let i = 0; i < ca.length; i++) {
            let c = ca[i];
            while (c.charAt(0) === ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) === 0) {
                try {
                    return JSON.parse(c.substring(nameEQ.length, c.length));
                } catch (e) {
                    console.error("Error parsing cookie:", name, e);
                    return null;
                }
            }
        }
        return null;
    }
    
    // --- Session Storage (в памяти) + Cookies как запас для Facebook WebView ---
    var sessionStorage = {}; // Основное хранение в памяти для сессии

    function setToLocalStorage(key, value) {
        try {
            // Основное хранение в памяти (session-only)
            sessionStorage[key] = value;

            // Запасное хранение в cookies (на случай если понадобится)
            try {
                setCookie(key + '_backup', value, 1); // 1 день как запас
            } catch (e) {
                console.warn("Could not save backup to cookie:", key, e);
            }
        } catch (e) {
            console.warn("Could not save to session storage:", key, e);
        }
    }

    function getFromLocalStorage(key) {
        try {
            // Сначала пробуем из памяти (session)
            if (sessionStorage.hasOwnProperty(key) && sessionStorage[key] !== undefined) {
                return sessionStorage[key];
            }

            // Если нет в памяти, пробуем из cookies (запас)
            var backup = getCookie(key + '_backup');
            if (backup !== null) {
                sessionStorage[key] = backup; // Восстанавливаем в память
                return backup;
            }

            return null;
        } catch (e) {
            console.warn("Could not retrieve from session storage:", key, e);
            return null;
        }
    }

    // --- Helper Functions ---
    function getInitials(name) {
        if (!name || typeof name !== 'string') return '??';
        const parts = name.trim().split(' ').filter(p => p.length > 0);
        if (parts.length === 0) return '??';
        if (parts.length === 1) return parts[0].substring(0, 2).toUpperCase();
        return (parts[0][0] + (parts[parts.length - 1][0] || '')).toUpperCase();
    }

    function getRandomColor() {
        // Заменяем padStart() на совместимый код для старых WebView
        var hex = Math.floor(Math.random()*16777215).toString(16);
        while (hex.length < 6) {
            hex = '0' + hex;
        }
        return '#' + hex;
    }

    function generateCommentId() {
        return Date.now().toString(36) + Math.random().toString(36).substring(2);
    }
    
    // --- Rating Logic ---
    function updateRatingUIDisplay() {
        if (!ratingSection) return;
        const newAvgRating = productRatingData.totalVotes > 0 ? (productRatingData.sumOfRatings / productRatingData.totalVotes) : 0;
        if (avgRatingSpan) avgRatingSpan.textContent = newAvgRating.toFixed(1);
        if (totalVotesSpan) totalVotesSpan.textContent = productRatingData.totalVotes.toLocaleString('es-AR');

        ratingBarElements.forEach(barEl => {
            const starLevel = parseInt(barEl.dataset.starRating, 10);
            const countForStar = productRatingData.votesByStar[starLevel] || 0;
            const percentage = productRatingData.totalVotes > 0 ? (countForStar / productRatingData.totalVotes) * 100 : 0;
            const progressBar = barEl.querySelector('.fb-style-comments-bar-progress');
            const countSpan = barEl.querySelector('.fb-style-comments-bar-count');
            if (progressBar) progressBar.style.width = percentage.toFixed(1) + '%';
            if (countSpan) countSpan.textContent = countForStar.toLocaleString('es-AR');
        });

        stars.forEach((starEl, index) => {
            starEl.classList.toggle('active', (index + 1) <= productRatingData.currentUserRating);
            starEl.classList.remove('hovered');
        });
    }

    function handleStarClick(clickedRating) {
        const previousUserRating = productRatingData.currentUserRating;
        if (productRatingData.userVotedInSession && previousUserRating === clickedRating) return;

        if (productRatingData.userVotedInSession && previousUserRating !== null) {
            productRatingData.votesByStar[previousUserRating]--;
            productRatingData.sumOfRatings -= previousUserRating;
        } else if (!productRatingData.userVotedInSession && previousUserRating !== null) {
            productRatingData.votesByStar[previousUserRating]--; 
            productRatingData.sumOfRatings -= previousUserRating;
        } else if (!productRatingData.userVotedInSession && previousUserRating === null) {
            productRatingData.totalVotes++;
        }
        
        productRatingData.votesByStar[clickedRating] = (productRatingData.votesByStar[clickedRating] || 0) + 1;
        productRatingData.sumOfRatings += clickedRating;
        productRatingData.currentUserRating = clickedRating;
        productRatingData.userVotedInSession = true; 

        setToLocalStorage(LS_KEY_RATING, clickedRating);
        updateRatingUIDisplay();

        if (userRatingFeedbackEl) {
            userRatingFeedbackEl.textContent = "¡Gracias por tu calificación!";
            userRatingFeedbackEl.style.display = 'block';
            setTimeout(() => { if(userRatingFeedbackEl) userRatingFeedbackEl.style.display = 'none'; }, 3000);
        }
        if (starsContainer) starsContainer.classList.add('user-has-selected-rating');
    }
    
    function initializeRatingSystem() {
        if (!ratingSection || !starsContainer) return;
        const storedUserRating = getFromLocalStorage(LS_KEY_RATING);
        if (storedUserRating !== null && typeof storedUserRating === 'number') {
            productRatingData.currentUserRating = storedUserRating;
            if (userRatingFeedbackEl) {
                 userRatingFeedbackEl.textContent = `Tu calificación anterior: ${storedUserRating} ${storedUserRating === 1 ? 'estrella' : 'estrellas'}. Puedes cambiarla.`;
                 userRatingFeedbackEl.style.display = 'block';
            }
            starsContainer.classList.add('user-has-selected-rating');
        }
        updateRatingUIDisplay();
        stars.forEach(starEl => {
            // Убираем hover события для мобильных устройств (не работают в touch)
            // Оставляем только click для совместимости с мобильными
            starEl.addEventListener('click', (e) => {
                const rating = parseInt(e.currentTarget.dataset.rating, 10);
                handleStarClick(rating);
            });

            // Добавляем touch события для лучшей отзывчивости на мобильных
            starEl.addEventListener('touchstart', function(e) {
                e.preventDefault(); // Предотвращаем двойное срабатывание
                this.style.transform = 'scale(1.1)'; // Визуальная обратная связь
            });

            starEl.addEventListener('touchend', function(e) {
                e.preventDefault();
                this.style.transform = 'scale(1)';
                const rating = parseInt(this.dataset.rating, 10);
                handleStarClick(rating);
            });
        });
    }

    // --- Render Comment Functions ---
    function createCommentHTML(commentData, depth = 0) {
        // Безопасное экранирование пользовательских данных
        const authorNameDisplay = escapeHtml(commentData.author) || "Anónimo";
        const commentTextDisplay = commentData.text ? escapeHtml(commentData.text).replace(/\n/g, '<br>') : "";
        let avatarHTML;
        if (commentData.isCompany) {
            avatarHTML = `
                <div class="fb-style-comments-avatar fb-style-comments-company-avatar">
                    <img src="${commentData.avatarLogo || 'https://via.placeholder.com/40x40.png?text=RG'}" alt="${authorNameDisplay} Logo">
                </div>`;
        } else {
            avatarHTML = `<div class="fb-style-comments-avatar" style="background-color: ${commentData.avatarColor || getRandomColor()};"><span>${getInitials(commentData.author)}</span></div>`;
        }
        const imageHTML = commentData.image ? `
            <div class="fb-style-comments-image-attachment">
                <img src="${commentData.image}" alt="Adjunto por ${authorNameDisplay}">
            </div>` : '';

        // Кнопка "Responder" и форма ответа показываются, если текущая глубина меньше MAX_REPLY_DEPTH
        const showReplyControls = depth < MAX_REPLY_DEPTH;
        const replyButtonHTML = showReplyControls ? `<button class="fb-style-comments-reply-btn">Responder</button>` : '';
        const replyFormContainerHTML = showReplyControls ? '<div class="fb-style-comments-reply-form-container" style="display: none;"></div>' : '';
        
        const likesText = commentData.userLiked ? 'Ya no me gusta' : 'Me gusta';
        const likedClass = commentData.userLiked ? 'liked' : '';

        return `
            <div class="fb-style-comments-comment-main">
                ${avatarHTML}
                <div class="fb-style-comments-comment-content">
                    <div class="fb-style-comments-comment-bubble">
                        <span class="fb-style-comments-author-name">${authorNameDisplay}</span>
                        <p class="fb-style-comments-text">${commentTextDisplay}</p>
                        ${imageHTML}
                    </div>
                    <div class="fb-style-comments-actions">
                        <span class="fb-style-comments-timestamp">${commentData.timestamp || 'Ahora mismo'}</span>
                        <button class="fb-style-comments-like-btn ${likedClass}" data-liked="${commentData.userLiked || false}">${likesText}</button>
                        ${replyButtonHTML}
                        <span class="fb-style-comments-likes-count">👍 <span class="fb-style-comments-count">${commentData.likes || 0}</span></span>
                    </div>
                </div>
            </div>
            <div class="fb-style-comments-replies-container">
                ${commentData.replies && commentData.replies.map(reply => createCommentHTML(reply, depth + 1)).join('') || ''}
            </div>
            ${replyFormContainerHTML}
        `;
    }
    
    function renderComment(commentData, parentElement, prepend = false) {
        const commentElementWrapper = document.createElement('div');
        commentElementWrapper.className = 'fb-style-comments-comment-item';
        if (parentElement && parentElement.classList.contains('fb-style-comments-replies-container')) {
            commentElementWrapper.classList.add('fb-style-comments-reply-item');
        }
        commentElementWrapper.dataset.commentId = commentData.id;
        commentElementWrapper.dataset.dynamicRendered = "true";
        
        let currentDepth = 0;
        let el = parentElement;
        // Определяем глубину, поднимаясь по DOM до commentsList
        while(el && commentsList && el !== commentsList && currentDepth <= MAX_REPLY_DEPTH + 3) { // +3 для безопасности
            if (el.classList.contains('fb-style-comments-replies-container')) {
                currentDepth++;
            }
            el = el.parentElement;
        }
        commentElementWrapper.innerHTML = createCommentHTML(commentData, currentDepth);

        if (prepend && parentElement) {
            // Заменяем prepend() на совместимый код для старых WebView
            if (parentElement.firstChild) {
                parentElement.insertBefore(commentElementWrapper, parentElement.firstChild);
            } else {
                parentElement.appendChild(commentElementWrapper);
            }
        } else if (parentElement) {
            parentElement.appendChild(commentElementWrapper);
        }
        attachEventListenersToComment(commentElementWrapper);
    }

    function loadComments() {
        if (!commentsList) return;

        // Сначала обрабатываем ВСЕ статические комментарии из HTML (включая вложенные)
        var allStaticComments = document.querySelectorAll('#fb-style-comments-list .fb-style-comments-comment-item');
        console.log('Found static comments:', allStaticComments.length);

        for (var i = 0; i < allStaticComments.length; i++) {
            var commentEl = allStaticComments[i];
            if (!commentEl.dataset.dynamicRendered) {
                console.log('Processing static comment:', commentEl.dataset.commentId);
                attachEventListenersToComment(commentEl);
                commentEl.dataset.dynamicRendered = "true";
            }
        }

        // Затем загружаем и добавляем динамические комментарии из cookie
        const savedComments = getCookie(COOKIE_NAME_COMMENTS);
        if (Array.isArray(savedComments) && savedComments.length > 0) {
            for (var j = 0; j < savedComments.length; j++) {
                var comment = savedComments[j];
                if (comment && typeof comment === 'object' && comment.id) {
                   renderComment(comment, commentsList, true);
                } else {
                    console.warn("Invalid comment data in cookie:", comment);
                }
            }
        }
    }
    
    function saveCommentToCookie(newCommentData) {
        let comments = getCookie(COOKIE_NAME_COMMENTS) || [];
        if (!Array.isArray(comments)) comments = [];
        comments = comments.filter(c => c && c.id !== newCommentData.id); 
        comments.unshift(newCommentData); 
        setCookie(COOKIE_NAME_COMMENTS, comments.slice(0, 50), 7); 
    }

    // --- Image Upload Handlers ---
    var selectedImageData = null;

    if (imageUploadBtn) {
        imageUploadBtn.addEventListener('click', function() {
            imageUploadInput.click();
        });
    }

    if (imageUploadInput) {
        imageUploadInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file && file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    selectedImageData = e.target.result;
                    previewImg.src = selectedImageData;
                    imagePreview.style.display = 'block';
                };
                reader.readAsDataURL(file);
            }
        });
    }

    if (removeImageBtn) {
        removeImageBtn.addEventListener('click', function() {
            selectedImageData = null;
            imagePreview.style.display = 'none';
            imageUploadInput.value = '';
        });
    }

    // --- Event Handlers for Comments ---
    if (mainCommentForm) {
        mainCommentForm.addEventListener('submit', function(e) {
            e.preventDefault();
            if (!authorNameInput || !authorTextInput) return;
            const authorName = authorNameInput.value.trim();
            const authorText = authorTextInput.value.trim();
            if (authorName && authorText) {
                const newCommentData = {
                    id: generateCommentId(), author: authorName, text: authorText,
                    timestamp: 'Ahora mismo', likes: 0, userLiked: false,
                    avatarColor: getRandomColor(), replies: [],
                    image: selectedImageData // Добавляем изображение если есть
                };
                if (commentsList) renderComment(newCommentData, commentsList, true);
                saveCommentToCookie(newCommentData);
                setToLocalStorage(LS_KEY_AUTHOR_NAME, authorName);
                authorTextInput.value = '';
                authorTextInput.style.height = 'auto';
                // Очищаем изображение после отправки
                selectedImageData = null;
                imagePreview.style.display = 'none';
                imageUploadInput.value = '';
            } else if (!authorName) {
                authorNameInput.focus(); alert("Por favor, ingresa tu nombre y apellido.");
            } else if (!authorText) {
                authorTextInput.focus(); alert("Por favor, escribe un comentario.");
            }
        });
    }
    
    if (authorNameInput) {
        const savedAuthorName = getFromLocalStorage(LS_KEY_AUTHOR_NAME);
        if (savedAuthorName) authorNameInput.value = savedAuthorName;
        authorNameInput.addEventListener('input', () => {
            setToLocalStorage(LS_KEY_AUTHOR_NAME, authorNameInput.value.trim());
        });
    }
    if(authorTextInput) {
        authorTextInput.addEventListener('input', function() {
            this.style.height = 'auto'; this.style.height = (this.scrollHeight) + 'px';
        });
    }

    function handleLike(e) {
        // Более надежный поиск кнопки лайка
        var likeButton = e.target;
        if (!likeButton.classList.contains('fb-style-comments-like-btn')) {
            likeButton = e.target.closest('.fb-style-comments-like-btn');
        }
        if (!likeButton) {
            console.log('Like button not found');
            return;
        }

        var liked = likeButton.dataset.liked === 'true';
        var actionsContainer = likeButton;
        var attempts = 0;
        while (actionsContainer && attempts < 5) {
            actionsContainer = actionsContainer.parentNode;
            attempts++;
            console.log('Looking for actions container, checking:', actionsContainer ? actionsContainer.className : 'null');
            if (actionsContainer && actionsContainer.classList && actionsContainer.classList.contains('fb-style-comments-actions')) {
                break;
            }
        }
        if (!actionsContainer || !actionsContainer.classList.contains('fb-style-comments-actions')) {
            console.log('Actions container not found after', attempts, 'attempts');
            return;
        }

        var likesCountContainer = actionsContainer.querySelector('.fb-style-comments-likes-count');
        if (!likesCountContainer) {
            console.log('Likes count container not found');
            return;
        }

        var likesCountSpan = likesCountContainer.querySelector('.fb-style-comments-count');
        if (!likesCountSpan) {
            console.log('Likes count span not found in container:', likesCountContainer);
            return;
        }

        console.log('Found likes count span:', likesCountSpan, 'current text:', likesCountSpan.textContent);

        var currentLikes = parseInt(likesCountSpan.textContent, 10);
        if (isNaN(currentLikes)) currentLikes = 0;

        liked = !liked;
        likeButton.dataset.liked = String(liked);

        if (liked) {
            likeButton.classList.add('liked');
            likeButton.textContent = 'Ya no me gusta';
            currentLikes++;
        } else {
            likeButton.classList.remove('liked');
            likeButton.textContent = 'Me gusta';
            currentLikes = Math.max(0, currentLikes - 1);
        }

        console.log('Setting new count to:', currentLikes);

        // Принудительно обновляем DOM несколькими способами
        likesCountSpan.textContent = currentLikes;
        likesCountSpan.innerHTML = currentLikes;

        // Принудительно перерисовываем элемент
        likesCountSpan.style.display = 'none';
        likesCountSpan.offsetHeight; // Принудительный reflow
        likesCountSpan.style.display = '';

        console.log('After setting, span text is:', likesCountSpan.textContent);
        console.log('After setting, span innerHTML is:', likesCountSpan.innerHTML);

        console.log('Like processed successfully, new count:', currentLikes);
    }

    function handleReplyToggle(e) {
        // Более надежный поиск кнопки ответа
        var replyButton = e.target;
        if (!replyButton.classList.contains('fb-style-comments-reply-btn')) {
            replyButton = e.target.closest('.fb-style-comments-reply-btn');
        }
        if (!replyButton) {
            console.log('Reply button not found');
            return;
        }

        // Ищем родительский комментарий - поднимаемся выше по DOM
        var commentItem = replyButton;
        var attempts = 0;
        while (commentItem && attempts < 10) {
            commentItem = commentItem.parentNode;
            attempts++;
            console.log('Checking element:', commentItem ? commentItem.className : 'null', 'attempt:', attempts);
            if (commentItem && commentItem.classList && commentItem.classList.contains('fb-style-comments-comment-item')) {
                break;
            }
        }

        if (!commentItem || !commentItem.classList.contains('fb-style-comments-comment-item')) {
            console.log('Comment item not found after', attempts, 'attempts');
            return;
        }

        console.log('Found comment item:', commentItem.dataset.commentId, 'classes:', commentItem.className);

        var replyFormContainer = commentItem.querySelector('.fb-style-comments-reply-form-container');
        if (!replyFormContainer) {
            console.log('Reply form container not found for comment:', commentItem.dataset.commentId);
            console.log('Comment item HTML:', commentItem.outerHTML.substring(0, 200) + '...');

            // Попробуем найти контейнер в родительском элементе (для вложенных комментариев)
            var parentComment = commentItem.parentNode;
            while (parentComment && !parentComment.classList.contains('fb-style-comments-comment-item')) {
                parentComment = parentComment.parentNode;
            }
            if (parentComment) {
                replyFormContainer = parentComment.querySelector('.fb-style-comments-reply-form-container');
                console.log('Found reply container in parent:', !!replyFormContainer);
            }

            if (!replyFormContainer) {
                console.log('Still no reply form container found');
                return;
            }
        }

        if (replyFormContainer.style.display === 'none' || replyFormContainer.innerHTML.trim() === '') {
            replyFormContainer.style.display = 'block';
            replyFormContainer.innerHTML = `
                <form class="fb-style-comments-reply-form">
                    <input type="text" placeholder="Tu nombre" class="fb-style-comments-reply-name" required style="width: 100%; margin-bottom: 8px; padding: 8px; border: 1px solid #ccd0d5; border-radius: 6px; font-family: inherit;">
                    <textarea placeholder="Escribe una respuesta..." rows="1" required></textarea>
                    <button type="submit" class="fb-style-comments-submit-btn">Responder</button>
                </form>`;
            const newReplyForm = replyFormContainer.querySelector('.fb-style-comments-reply-form');
            const newReplyTextarea = newReplyForm.querySelector('textarea');
            newReplyTextarea.addEventListener('input', function() {
                this.style.height = 'auto'; this.style.height = (this.scrollHeight) + 'px';
            });
            // Заменяем requestAnimationFrame на setTimeout для совместимости с старыми WebView
            setTimeout(function() {
                newReplyTextarea.focus();
                newReplyTextarea.style.height = (newReplyTextarea.scrollHeight) + 'px';
            }, 0);
            newReplyForm.addEventListener('submit', function(submitEvent) {
                submitEvent.preventDefault();
                const replyNameInput = newReplyForm.querySelector('.fb-style-comments-reply-name');
                const replyText = newReplyTextarea.value.trim();
                const replierName = replyNameInput ? replyNameInput.value.trim() : '';

                if (replyText && replierName) {
                    const newReplyData = {
                        id: generateCommentId(), author: replierName, text: replyText,
                        timestamp: 'Ahora mismo', likes: 0, userLiked: false,
                        avatarColor: getRandomColor(), replies: []
                    };
                    const parentRepliesContainer = commentItem.querySelector('.fb-style-comments-replies-container');
                    if (parentRepliesContainer) renderComment(newReplyData, parentRepliesContainer, false);
                    newReplyTextarea.value = '';
                    if (replyNameInput) replyNameInput.value = '';
                    replyFormContainer.style.display = 'none'; replyFormContainer.innerHTML = '';
                } else if (!replierName) {
                    if (replyNameInput) replyNameInput.focus();
                    alert("Por favor, ingresa tu nombre para responder.");
                } else if (!replyText) {
                    newReplyTextarea.focus(); alert("Por favor, escribe una respuesta.");
                }
            });
        } else {
            replyFormContainer.style.display = 'none'; replyFormContainer.innerHTML = ''; 
        }
    }

    function attachEventListenersToComment(commentElement) {
        if (!commentElement) return;

        console.log('Attaching events to comment:', commentElement.dataset.commentId);

        // Добавляем обработчик событий для текущего элемента
        commentElement.addEventListener('click', function(e) {
            console.log('Click detected on:', e.target.className, 'tag:', e.target.tagName);

            if (e.target.classList.contains('fb-style-comments-like-btn') || e.target.closest('.fb-style-comments-like-btn')) {
                console.log('Like button clicked');
                e.preventDefault();
                e.stopPropagation();
                handleLike(e);
            } else if (e.target.classList.contains('fb-style-comments-reply-btn') || e.target.closest('.fb-style-comments-reply-btn')) {
                console.log('Reply button clicked');
                e.preventDefault();
                e.stopPropagation();
                handleReplyToggle(e);
            } else if (e.target.classList.contains('fb-style-comments-submit-btn')) {
                console.log('Submit button clicked - allowing default behavior');
                // НЕ блокируем submit кнопки - пусть работают как обычно
                return;
            } else {
                console.log('Other element clicked:', e.target.tagName, e.target.className);
            }
        });

        console.log('Events attached successfully to:', commentElement.dataset.commentId);
    }
    
    // --- Initialization ---
    if (ratingSection) initializeRatingSystem();
    if (commentsList) loadComments(); 
    if (authorNameInput) {
        authorNameInput.addEventListener('input', function() {
            setToLocalStorage(LS_KEY_AUTHOR_NAME, this.value.trim());
        });
    }
});