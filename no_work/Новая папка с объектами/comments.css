body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    margin: 0;
    padding: 10px;
    background-color: #f0f2f5; 
    color: #050505;
    -webkit-tap-highlight-color: transparent; /* Убрать подсветку при тапе на мобильных */
    box-sizing: border-box; /* Для более предсказуемой работы с padding/border */
}

*, *:before, *:after {
    box-sizing: inherit;
}

.fb-style-comments-app-container {
    max-width: 600px; 
    margin: 0 auto;
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    padding: 15px;
}

/* --- Rating Section --- */
.fb-style-comments-rating-section {
    padding-bottom: 20px;
    margin-bottom: 20px;
    border-bottom: 1px solid #e0e0e0;
}

.fb-style-comments-rating-title {
    font-size: 1.1em;
    font-weight: 600;
    margin-bottom: 10px;
    color: #1c1e21;
}

.fb-style-comments-rating-container {
    /* Fallback для старых WebView без flexbox */
    display: table;
    width: 100%;
    margin-bottom: 15px;
    /* Современный flexbox для поддерживающих браузеров */
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
}

.fb-style-comments-stars .fb-style-comments-star {
    font-size: 1.8em;
    color: #bec3c9; /* Серые по умолчанию */
    cursor: pointer;
    padding: 8px; /* Увеличиваем touch target для мобильных */
    margin: 0 2px;
    transition: color 0.1s ease-in-out, transform 0.1s ease-in-out;
    /* Минимальный размер touch target 44px для мобильных */
    min-width: 44px;
    min-height: 44px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    /* Убираем подсветку при тапе на мобильных */
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
}

/* Ховер только если пользователь еще не выбрал */
.fb-style-comments-stars:not(.user-has-selected-rating) .fb-style-comments-star:hover, 
.fb-style-comments-stars .fb-style-comments-star.hovered { 
    color: #fadb14; /* Ярче желтый при наведении */
}

.fb-style-comments-stars .fb-style-comments-star.active {
    color: #FFD700; /* Gold - для выбранных */
}

.fb-style-comments-rating-stats {
    margin-left: 10px;
    font-size: 0.95em;
    color: #606770;
}

.fb-style-comments-rating-bars {
    font-size: 0.9em;
    /* Фиксированная высота чтобы не дергалось при анимациях */
    min-height: 130px; /* 5 полосок × 26px высота */
}

.fb-style-comments-rating-bar {
    /* Fallback для старых WebView */
    display: table;
    width: 100%;
    margin-bottom: 5px;
    color: #606770;
    /* Современный flexbox */
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    /* Фиксированная высота для каждой полоски */
    height: 22px;
    overflow: visible; /* Для анимаций свечения */
}

.fb-style-comments-bar-label {
    width: 30px;
    font-weight: 500;
    /* Fallback для старых WebView */
    display: table-cell;
    vertical-align: middle;
    /* Современный flexbox */
    -webkit-flex-shrink: 0;
    -ms-flex-negative: 0;
    flex-shrink: 0;
}

.fb-style-comments-bar-fill {
    /* Fallback для старых WebView */
    display: table-cell;
    width: 100%;
    vertical-align: middle;
    /* Современный flexbox */
    -webkit-box-flex: 1;
    -webkit-flex-grow: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    height: 8px;
    background-color: #e9ebee;
    border-radius: 4px;
    margin: 0 8px;
    overflow: hidden;
}

.fb-style-comments-bar-progress {
    height: 100%;
    background: linear-gradient(90deg, #FFD700, #FFA500);
    border-radius: 4px;
    transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.fb-style-comments-bar-progress.animating::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent);
    animation: shimmer 1.2s ease-in-out;
}

.fb-style-comments-bar-progress.animating {
    animation: barGlow 1.2s ease-in-out;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

@keyframes barGlow {
    0% {
        background: linear-gradient(90deg, #FFD700, #FFA500);
        box-shadow: none;
    }
    50% {
        background: linear-gradient(90deg, #FFD700, #FF6B35);
        box-shadow: 0 0 20px rgba(255, 215, 0, 0.8);
        transform: scaleY(1.1);
    }
    100% {
        background: linear-gradient(90deg, #FFD700, #FFA500);
        box-shadow: none;
        transform: scaleY(1);
    }
}

.fb-style-comments-bar-count {
    min-width: 30px;
    text-align: right;
    /* Fallback для старых WebView */
    display: table-cell;
    vertical-align: middle;
    /* Современный flexbox */
    -webkit-flex-shrink: 0;
    -ms-flex-negative: 0;
    flex-shrink: 0;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 600;
}

.fb-style-comments-bar-count.updated {
    color: #1877f2;
    transform: scale(1.2);
    animation: countPulse 0.6s ease-in-out;
}

@keyframes countPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.3); color: #FFD700; }
    100% { transform: scale(1.2); color: #1877f2; }
}

/* Стили для среднего рейтинга (без анимаций) */
.fb-style-comments-avg-rating {
    font-weight: 700;
    font-size: 1.2em;
    color: #1c1e21;
}

.fb-style-comments-total-votes {
    font-weight: 600;
    color: #606770;
}

.fb-style-comments-user-rating-feedback {
    font-weight: 500;
    color: #bcc0c4; /* Серый по умолчанию */
    opacity: 0.7;
    transition: all 0.3s ease-in-out;
}

.fb-style-comments-user-rating-feedback.visible {
    color: #1877f2; /* Синий когда активно */
    opacity: 1;
    transform: scale(1.05);
}

/* --- Comment Form Section --- */
.fb-style-comments-comment-form-section {
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e0e0e0;
}
.fb-style-comments-form-title {
    font-size: 1.1em;
    font-weight: 600;
    margin-bottom: 15px;
    color: #1c1e21;
}
.fb-style-comments-form-group {
    margin-bottom: 10px;
}
.fb-style-comments-form-group input[type="text"],
.fb-style-comments-form-group textarea {
    width: 100%; 
    padding: 10px;
    border: 1px solid #ccd0d5;
    border-radius: 6px;
    font-size: 1em;
    background-color: #f5f6f7;
    -webkit-appearance: none; /* Убрать стандартные стили iOS */
    appearance: none;
    font-family: inherit; /* Наследование шрифта */
}
.fb-style-comments-form-group textarea {
    resize: vertical;
    min-height: 60px; /* Начальная высота */
    line-height: 1.4; /* Для лучшей читаемости */
    overflow-y: hidden; /* JS управляет высотой, чтобы избежать двойного скролла */
}
/* Старые стили кнопки удалены - используем новые стили ниже */

/* Простая мобильная форма комментариев */
.fb-style-comments-main-form {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 12px;
    border: 1px solid #e4e6ea;
}

.fb-style-comments-name-input {
    width: 100%;
    padding: 12px;
    border: 1px solid #ccd0d5;
    border-radius: 8px;
    font-size: 16px; /* Важно для мобильных - предотвращает zoom */
    font-family: inherit;
    background: white;
    outline: none;
    margin-bottom: 10px;
    -webkit-appearance: none;
    appearance: none;
}

.fb-style-comments-name-input:focus {
    border-color: #1877f2;
}

.fb-style-comments-text-input {
    width: 100%;
    padding: 12px;
    border: 1px solid #ccd0d5;
    border-radius: 8px;
    font-size: 16px; /* Важно для мобильных */
    font-family: inherit;
    background: white;
    outline: none;
    resize: none;
    min-height: 80px;
    margin-bottom: 10px;
    -webkit-appearance: none;
    appearance: none;
}

.fb-style-comments-text-input:focus {
    border-color: #1877f2;
}

.fb-style-comments-photo-btn {
    background: #42b883;
    color: white;
    border: none;
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 10px;
    -webkit-tap-highlight-color: transparent;
    -webkit-appearance: none;
    appearance: none;
    font-family: inherit;
}

.fb-style-comments-photo-btn:active {
    background: #369870;
}

.fb-style-comments-image-preview {
    margin-bottom: 10px;
    position: relative;
}

.fb-style-comments-image-preview img {
    max-width: 200px;
    max-height: 200px;
    border-radius: 8px;
    border: 1px solid #e4e6ea;
}

.fb-style-comments-remove-image {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #ff4444;
    color: white;
    border: none;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    -webkit-tap-highlight-color: transparent;
}

.fb-style-comments-remove-image:active {
    background: #cc0000;
}

.fb-style-comments-submit-btn {
    background-color: #1877f2;
    color: white;
    border: none;
    padding: 14px 24px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    font-size: 16px;
    width: 100%;
    -webkit-appearance: none;
    appearance: none;
    font-family: inherit;
    -webkit-tap-highlight-color: transparent;
}

.fb-style-comments-submit-btn:active {
    background-color: #166fe5;
}

/* Стили для SVG иконок */
.star-icon {
    width: 24px;
    height: 24px;
    filter: brightness(0) saturate(100%) invert(75%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(85%) contrast(95%);
    transition: filter 0.1s ease-in-out;
}

.fb-style-comments-star.active .star-icon {
    filter: brightness(0) saturate(100%) invert(85%) sepia(100%) saturate(2000%) hue-rotate(315deg) brightness(100%) contrast(100%);
}

.like-icon {
    width: 16px;
    height: 16px;
    margin-right: 6px;
    filter: brightness(0) saturate(100%) invert(45%) sepia(10%) saturate(500%) hue-rotate(200deg) brightness(95%) contrast(85%);
    transition: filter 0.1s ease-in-out;
}

.fb-style-comments-like-btn.liked .like-icon {
    filter: brightness(0) saturate(100%) invert(27%) sepia(100%) saturate(7000%) hue-rotate(220deg) brightness(100%) contrast(100%);
}

.upload-icon {
    width: 18px;
    height: 18px;
    margin-right: 8px;
    filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%);
}

/* --- Comments List --- */
.fb-style-comments-list-container {
    /* Стили для контейнера списка, если нужны */
}

.fb-style-comments-comment-item {
    margin-bottom: 15px;
    position: relative; 
}

.fb-style-comments-comment-main {
    /* Fallback для старых WebView */
    display: table;
    width: 100%;
    /* Современный flexbox */
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: start;
    -webkit-align-items: flex-start;
    -ms-flex-align: start;
    align-items: flex-start;
}

.fb-style-comments-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    margin-right: 10px;
    color: white;
    font-weight: 600;
    font-size: 0.9em;
    line-height: 36px;
    text-align: center;
    overflow: hidden; /* Если инициалы слишком длинные */
    /* Fallback для старых WebView */
    display: table-cell;
    vertical-align: top;
    /* Современный flexbox */
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-flex-shrink: 0;
    -ms-flex-negative: 0;
    flex-shrink: 0;
}
.fb-style-comments-company-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    /* Убираем object-fit для совместимости с старыми WebView */
    /* object-fit: cover; */
}


.fb-style-comments-comment-content {
    /* Fallback для старых WebView */
    display: table-cell;
    width: 100%;
    vertical-align: top;
    /* Современный flexbox */
    -webkit-box-flex: 1;
    -webkit-flex-grow: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    /* Заменяем calc() на фиксированное значение для совместимости */
    max-width: 85%; /* Примерно calc(100% - 46px) */
}

.fb-style-comments-comment-bubble {
    background-color: #f0f2f5; 
    padding: 8px 12px;
    border-radius: 18px; 
    margin-bottom: 5px;
    word-wrap: break-word; 
    overflow-wrap: break-word; 
}

.fb-style-comments-author-name {
    font-weight: 600;
    font-size: 0.9em;
    color: #050505;
    display: block; 
    margin-bottom: 2px;
}
.fb-style-comments-company-reply .fb-style-comments-author-name {
    color: #1877f2; 
}


.fb-style-comments-text {
    font-size: 0.95em;
    line-height: 1.35;
    margin: 0;
    color: #050505;
    white-space: pre-wrap; /* Сохраняет переносы строк из textarea */
}

.fb-style-comments-image-attachment {
    margin-top: 8px;
}
.fb-style-comments-image-attachment img {
    max-width: 100%;
    height: auto; 
    border-radius: 8px;
    display: block; 
}

.fb-style-comments-actions {
    font-size: 0.8em;
    color: #606770;
    padding-left: 12px;
    /* Fallback для старых WebView */
    display: block;
    /* Современный flexbox с префиксами */
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}

.fb-style-comments-actions > * {
    margin-right: 8px; /* Отступы между элементами */
}
.fb-style-comments-actions > *:last-child {
    margin-right: 0;
}

.fb-style-comments-actions button {
    background: none;
    border: none;
    color: #606770;
    font-weight: 600;
    cursor: pointer;
    /* Увеличиваем touch target для мобильных */
    padding: 8px 12px;
    margin: 0;
    font-size: 1em;
    font-family: inherit;
    vertical-align: middle;
    /* Минимальный размер для touch */
    min-height: 44px;
    border-radius: 4px;
    /* Убираем подсветку при тапе на мобильных */
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
    transition: opacity 0.15s ease-in-out;
    /* Flexbox для выравнивания иконки и текста */
    display: inline-flex;
    align-items: center;
    gap: 6px;
}
.fb-style-comments-actions button:hover,
.fb-style-comments-actions button:active {
    text-decoration: underline;
}
.fb-style-comments-actions .fb-style-comments-like-btn.liked {
    color: #1877f2; 
}

.fb-style-comments-timestamp {
    /* margin-right: 8px; Уже установлено через .fb-style-comments-actions > * */
}
/* Убираем старые стили для отдельного счетчика лайков, теперь он внутри кнопки */
.fb-style-comments-likes-count .fb-style-comments-count {
    margin-left: 4px;
}

/* --- Replies --- */
.fb-style-comments-replies-container {
    margin-left: 46px; /* Отступ для ответов (ширина аватара + margin) */
    padding-top: 8px;
}

/* Ответ 1-го уровня */
.fb-style-comments-reply-item .fb-style-comments-avatar {
    width: 28px; 
    height: 28px;
    font-size: 0.8em;
    line-height: 28px;
}
.fb-style-comments-reply-item .fb-style-comments-comment-content {
     /* Заменяем calc() на фиксированное значение для совместимости */
     max-width: 82%; /* Примерно calc(100% - 38px) */
}

/* Ответ 2-го уровня (ответ на ответ) */
.fb-style-comments-replies-container .fb-style-comments-replies-container { 
    margin-left: 38px; /* Еще меньший отступ для следующего уровня */
}
.fb-style-comments-reply-item .fb-style-comments-reply-item .fb-style-comments-avatar { 
     width: 24px;
     height: 24px;
     font-size: 0.7em;
     line-height: 24px;
}
.fb-style-comments-reply-item .fb-style-comments-reply-item .fb-style-comments-comment-content {
    /* Заменяем calc() на фиксированное значение для совместимости */
    max-width: 80%; /* Примерно calc(100% - 34px) */
}


/* Простая мобильная форма ответов */
.fb-style-comments-reply-form-container {
    margin-top: 10px;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 12px;
    border: 1px solid #e4e6ea;
}

.fb-style-comments-reply-form-container input[type="text"] {
    width: 100%;
    padding: 12px;
    border: 1px solid #ccd0d5;
    border-radius: 8px;
    font-size: 16px; /* Важно для мобильных */
    font-family: inherit;
    background: white;
    outline: none;
    margin-bottom: 10px;
    -webkit-appearance: none;
    appearance: none;
}

.fb-style-comments-reply-form-container input[type="text"]:focus {
    border-color: #1877f2;
}

.fb-style-comments-reply-form-container textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ccd0d5;
    border-radius: 8px;
    font-size: 16px; /* Важно для мобильных */
    font-family: inherit;
    background: white;
    outline: none;
    resize: none;
    min-height: 60px;
    margin-bottom: 10px;
    -webkit-appearance: none;
    appearance: none;
}

.fb-style-comments-reply-form-container textarea:focus {
    border-color: #1877f2;
}

.fb-style-comments-reply-form-container button.fb-style-comments-submit-btn {
    background-color: #1877f2;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    font-size: 14px;
    width: 100%;
    -webkit-appearance: none;
    appearance: none;
    font-family: inherit;
    -webkit-tap-highlight-color: transparent;
}

.fb-style-comments-reply-form-container button.fb-style-comments-submit-btn:active {
    background-color: #166fe5;
}