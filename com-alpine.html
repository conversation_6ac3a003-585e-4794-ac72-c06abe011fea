<!DOCTYPE html>
<html lang="es-AR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Bloque de Comentarios Estilo Facebook Móvil</title>
    <link rel="stylesheet" href="comments.css">
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
</head>
<body>

    <div class="fb-style-comments-app-container" x-data="commentsApp()">

        <!-- Секция Рейтинга -->
        <div class="fb-style-comments-rating-section">
            <div class="fb-style-comments-rating-title">Califica este producto</div>
            <div class="fb-style-comments-rating-container">
                <div class="fb-style-comments-stars">
                    <template x-for="star in 5" :key="star">
                        <span class="fb-style-comments-star"
                              :class="{ 'active': star <= rating.userRating }"
                              @click="setRating(star)"
                              @touchstart.prevent="handleStarTouch(star, $event)"
                              @touchend.prevent="setRating(star)">
                            <img src="img_coment/star.svg" alt="Star" class="star-icon">
                        </span>
                    </template>
                </div>
                <div class="fb-style-comments-rating-stats">
                    <span class="fb-style-comments-avg-rating" x-text="rating.average"></span> de 5 
                    (<span class="fb-style-comments-total-votes" x-text="rating.total"></span> opiniones)
                </div>
            </div>
            
            <div class="fb-style-comments-rating-bars">
                <template x-for="stars in getSortedStars()" :key="stars">
                    <div class="fb-style-comments-rating-bar">
                        <span class="fb-style-comments-bar-label" x-text="stars + '★'"></span>
                        <div class="fb-style-comments-bar-fill">
                            <div class="fb-style-comments-bar-progress"
                                 :style="`width: ${(rating.breakdown[stars] / rating.total * 100).toFixed(1)}%`"></div>
                        </div>
                        <span class="fb-style-comments-bar-count" x-text="rating.breakdown[stars]"></span>
                    </div>
                </template>
            </div>
            
            <div x-show="rating.feedback" 
                 x-text="rating.feedback" 
                 class="fb-style-comments-user-rating-feedback"
                 style="margin-top:10px; font-size:0.9em; color:#1877f2; text-align: center;"></div>
        </div>

        <!-- Форма добавления комментария -->
        <div class="fb-style-comments-comment-form-section">
            <h3 class="fb-style-comments-form-title">Deja tu comentario</h3>
            <form @submit.prevent="addComment()">
                <div class="fb-style-comments-form-group">
                    <input type="text" x-model="newComment.author" placeholder="Tu nombre y apellido" required>
                </div>
                <div class="fb-style-comments-form-group">
                    <textarea x-model="newComment.text" placeholder="Escribe un comentario..." rows="3" required></textarea>
                </div>
                <div class="fb-style-comments-form-group">
                    <input type="file" @change="handleImageUpload" accept="image/*" style="display: none;" x-ref="imageInput">
                    <button type="button" @click="$refs.imageInput.click()" class="fb-style-comments-image-upload-btn">
                        <img src="img_coment/imageInput.svg" alt="Upload" class="upload-icon">
                        Agregar foto
                    </button>
                    <div x-show="newComment.image" style="margin-top: 10px;">
                        <img :src="newComment.image" style="max-width: 200px; max-height: 200px; border-radius: 8px;">
                        <button type="button" @click="removeImage()" style="margin-left: 10px; background: #ff4444; color: white; border: none; padding: 5px 10px; border-radius: 4px;">Quitar</button>
                    </div>
                </div>
                <button type="submit" class="fb-style-comments-submit-btn">Publicar comentario</button>
            </form>
        </div>

        <!-- Список комментариев -->
        <div class="fb-style-comments-list-container">
            <template x-for="comment in allComments" :key="comment.id">
                <div class="fb-style-comments-comment-item" :data-comment-id="comment.id">
                    <div class="fb-style-comments-comment-main">
                        <div class="fb-style-comments-avatar" 
                             :class="{ 'fb-style-comments-company-avatar': comment.isCompany }"
                             :style="comment.isCompany ? '' : `background-color: ${comment.avatarColor}`">
                            <img x-show="comment.isCompany" :src="comment.avatarLogo" :alt="comment.author + ' Logo'">
                            <span x-show="!comment.isCompany" x-text="getInitials(comment.author)"></span>
                        </div>
                        <div class="fb-style-comments-comment-content">
                            <div class="fb-style-comments-comment-bubble">
                                <span class="fb-style-comments-author-name" 
                                      :class="{ 'fb-style-comments-company-reply': comment.isCompany }"
                                      x-text="comment.author"></span>
                                <p class="fb-style-comments-text" x-html="comment.text.replace(/\n/g, '<br>')"></p>
                                <div x-show="comment.image" class="fb-style-comments-image-attachment">
                                    <img :src="comment.image" :alt="'Adjunto por ' + comment.author">
                                </div>
                            </div>
                            <div class="fb-style-comments-actions">
                                <span class="fb-style-comments-timestamp" x-text="comment.timestamp"></span>
                                <button class="fb-style-comments-like-btn"
                                        :class="{ 'liked': comment.userLiked }"
                                        @click="toggleLike(comment)"
                                        @touchstart.prevent="handleButtonTouch($event)"
                                        @touchend.prevent="toggleLike(comment)">
                                    <img src="img_coment/like.svg" alt="Like" class="like-icon">
                                    <span x-text="comment.userLiked ? 'Ya no me gusta' : 'Me gusta'"></span>
                                    <span class="fb-style-comments-count" x-text="comment.likes"></span>
                                </button>
                                <button x-show="getDepth(comment) < 2"
                                        class="fb-style-comments-reply-btn"
                                        @click="toggleReplyForm(comment)"
                                        @touchstart.prevent="handleButtonTouch($event)"
                                        @touchend.prevent="toggleReplyForm(comment)">Responder</button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Ответы -->
                    <div x-show="comment.replies && comment.replies.length > 0" class="fb-style-comments-replies-container">
                        <template x-for="reply in comment.replies" :key="reply.id">
                            <div class="fb-style-comments-comment-item fb-style-comments-reply-item" :data-comment-id="reply.id">
                                <!-- Рекурсивная структура для ответов -->
                                <div class="fb-style-comments-comment-main">
                                    <div class="fb-style-comments-avatar" 
                                         :class="{ 'fb-style-comments-company-avatar': reply.isCompany }"
                                         :style="reply.isCompany ? '' : `background-color: ${reply.avatarColor}`">
                                        <img x-show="reply.isCompany" :src="reply.avatarLogo" :alt="reply.author + ' Logo'">
                                        <span x-show="!reply.isCompany" x-text="getInitials(reply.author)"></span>
                                    </div>
                                    <div class="fb-style-comments-comment-content">
                                        <div class="fb-style-comments-comment-bubble">
                                            <span class="fb-style-comments-author-name" 
                                                  :class="{ 'fb-style-comments-company-reply': reply.isCompany }"
                                                  x-text="reply.author"></span>
                                            <p class="fb-style-comments-text" x-html="reply.text.replace(/\n/g, '<br>')"></p>
                                            <div x-show="reply.image" class="fb-style-comments-image-attachment">
                                                <img :src="reply.image" :alt="'Adjunto por ' + reply.author">
                                            </div>
                                        </div>
                                        <div class="fb-style-comments-actions">
                                            <span class="fb-style-comments-timestamp" x-text="reply.timestamp"></span>
                                            <button class="fb-style-comments-like-btn"
                                                    :class="{ 'liked': reply.userLiked }"
                                                    @click="toggleLike(reply)"
                                                    @touchstart.prevent="handleButtonTouch($event)"
                                                    @touchend.prevent="toggleLike(reply)">
                                                <img src="img_coment/like.svg" alt="Like" class="like-icon">
                                                <span x-text="reply.userLiked ? 'Ya no me gusta' : 'Me gusta'"></span>
                                                <span class="fb-style-comments-count" x-text="reply.likes"></span>
                                            </button>
                                            <button x-show="getDepth(reply) < 2"
                                                    class="fb-style-comments-reply-btn"
                                                    @click="toggleReplyForm(reply)"
                                                    @touchstart.prevent="handleButtonTouch($event)"
                                                    @touchend.prevent="toggleReplyForm(reply)">Responder</button>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Форма ответа для reply -->
                                <div x-show="reply.showReplyForm" class="fb-style-comments-reply-form-container">
                                    <form @submit.prevent="addReply(reply)">
                                        <input type="text" x-model="reply.replyAuthor" placeholder="Tu nombre" required style="width: 100%; margin-bottom: 8px; padding: 8px; border: 1px solid #ccd0d5; border-radius: 6px; font-family: inherit;">
                                        <textarea x-model="reply.replyText" placeholder="Escribe una respuesta..." rows="1" required></textarea>
                                        <button type="submit" class="fb-style-comments-submit-btn">Responder</button>
                                    </form>
                                </div>
                            </div>
                        </template>
                    </div>
                    
                    <!-- Форма ответа для основного комментария -->
                    <div x-show="comment.showReplyForm" class="fb-style-comments-reply-form-container">
                        <form @submit.prevent="addReply(comment)">
                            <input type="text" x-model="comment.replyAuthor" placeholder="Tu nombre" required style="width: 100%; margin-bottom: 8px; padding: 8px; border: 1px solid #ccd0d5; border-radius: 6px; font-family: inherit;">
                            <textarea x-model="comment.replyText" placeholder="Escribe una respuesta..." rows="1" required></textarea>
                            <button type="submit" class="fb-style-comments-submit-btn">Responder</button>
                        </form>
                    </div>
                </div>
            </template>
        </div>

    </div>

    <script>
        // Полифиллы для старых WebView
        if (!Array.prototype.find) {
            Array.prototype.find = function(predicate) {
                for (var i = 0; i < this.length; i++) {
                    if (predicate(this[i], i, this)) return this[i];
                }
                return undefined;
            };
        }

        if (!Object.keys) {
            Object.keys = function(obj) {
                var keys = [];
                for (var key in obj) {
                    if (obj.hasOwnProperty(key)) keys.push(key);
                }
                return keys;
            };
        }

        function commentsApp() {
            return {
                // Рейтинг
                rating: {
                    userRating: 0,
                    previousUserRating: 0, // Для отслеживания предыдущего рейтинга
                    average: 4.8,
                    total: 3791,
                    breakdown: { 5: 3150, 4: 500, 3: 100, 2: 25, 1: 16 },
                    feedback: '',
                    userHasVoted: false // Флаг, что пользователь уже голосовал
                },
                
                // Новый комментарий
                newComment: {
                    author: '',
                    text: '',
                    image: null
                },
                
                // Все комментарии
                allComments: [
                    {
                        id: 'static-1',
                        author: 'Lucía Pérez',
                        text: '¡Me encantó el producto! Llegó súper rápido y la calidad es excelente. Lo recomiendo totalmente.',
                        timestamp: 'Hace 5 min',
                        likes: 12,
                        userLiked: false,
                        avatarColor: '#FFC300',
                        image: 'img_coment/6NFtqX6PxJe2 1.jpg',
                        isCompany: false,
                        showReplyForm: false,
                        replyAuthor: '',
                        replyText: '',
                        replies: [
                            {
                                id: 'static-1-1',
                                author: 'Martín Gómez',
                                text: 'Qué bueno Lucía! Estaba pensando en comprarlo. ¿El envío fue a CABA o GBA?',
                                timestamp: 'Hace 2 min',
                                likes: 3,
                                userLiked: false,
                                avatarColor: '#DAF7A6',
                                image: null,
                                isCompany: false,
                                showReplyForm: false,
                                replyAuthor: '',
                                replyText: '',
                                replies: [
                                    {
                                        id: 'static-1-1-1',
                                        author: 'Lucía Pérez',
                                        text: 'Sí Martín, a Palermo (CABA) llegó en 2 días! Súper eficiente.',
                                        timestamp: 'Ahora mismo',
                                        likes: 1,
                                        userLiked: false,
                                        avatarColor: '#FFC300',
                                        image: null,
                                        isCompany: false,
                                        replies: []
                                    }
                                ]
                            },
                            {
                                id: 'static-1-2',
                                author: 'Rhino Gold®',
                                text: '¡Hola Lucía! Muchas gracias por tu comentario, nos alegra saber que estás satisfecha con tu compra y la rapidez del envío. ¡Disfrútalo!',
                                timestamp: 'Hace 1 min',
                                likes: 8,
                                userLiked: false,
                                avatarColor: null,
                                image: null,
                                isCompany: true,
                                avatarLogo: 'img_coment/logo.png',
                                showReplyForm: false,
                                replyAuthor: '',
                                replyText: '',
                                replies: []
                            }
                        ]
                    },
                    {
                        id: 'static-2',
                        author: 'Carlos Sánchez',
                        text: 'Muy buen servicio al cliente, resolvieron mis dudas rápidamente antes de comprar. El producto parece de buena calidad.',
                        timestamp: 'Hace 2 horas',
                        likes: 25,
                        userLiked: false,
                        avatarColor: '#581845',
                        image: null,
                        isCompany: false,
                        showReplyForm: false,
                        replyAuthor: '',
                        replyText: '',
                        replies: []
                    },
                    {
                        id: 'static-3',
                        author: 'Ana Fernández',
                        text: '¿Alguien sabe si hacen envíos al interior del país? Soy de Córdoba.',
                        timestamp: 'Hace 3 horas',
                        likes: 7,
                        userLiked: false,
                        avatarColor: '#3498DB',
                        image: null,
                        isCompany: false,
                        showReplyForm: false,
                        replyAuthor: '',
                        replyText: '',
                        replies: []
                    }
                ],

                // Методы
                init() {
                    // Загружаем сохраненные данные
                    const savedRating = this.getFromStorage('userRating');
                    if (savedRating) {
                        this.rating.userRating = savedRating;
                        this.rating.previousUserRating = savedRating;
                        this.rating.userHasVoted = true;
                        this.rating.feedback = `Tu calificación anterior: ${savedRating} ${savedRating === 1 ? 'estrella' : 'estrellas'}. Puedes cambiarla.`;
                    }

                    const savedAuthor = this.getFromStorage('authorName');
                    if (savedAuthor) {
                        this.newComment.author = savedAuthor;
                    }
                },

                setRating(stars) {
                    // Если пользователь уже голосовал, убираем предыдущий голос
                    if (this.rating.userHasVoted && this.rating.previousUserRating > 0) {
                        this.rating.breakdown[this.rating.previousUserRating]--;
                        this.rating.total--;
                    }

                    // Добавляем новый голос
                    this.rating.breakdown[stars]++;
                    if (!this.rating.userHasVoted) {
                        this.rating.total++;
                    }

                    // Обновляем состояние
                    this.rating.previousUserRating = this.rating.userRating;
                    this.rating.userRating = stars;
                    this.rating.userHasVoted = true;

                    // Пересчитываем средний рейтинг
                    this.updateAverageRating();

                    this.rating.feedback = '¡Gracias por tu calificación!';
                    this.saveToStorage('userRating', stars);
                    setTimeout(() => { this.rating.feedback = ''; }, 3000);
                },

                updateAverageRating() {
                    let totalStars = 0;
                    for (let stars = 1; stars <= 5; stars++) {
                        totalStars += stars * this.rating.breakdown[stars];
                    }
                    this.rating.average = this.rating.total > 0 ? (totalStars / this.rating.total).toFixed(1) : 0;
                },

                getSortedStars: function() {
                    // Сортируем звезды по количеству голосов (больше голосов = выше в списке)
                    var self = this;
                    var keys = Object.keys(this.rating.breakdown);
                    var sorted = keys.sort(function(a, b) {
                        return self.rating.breakdown[b] - self.rating.breakdown[a];
                    });
                    var result = [];
                    for (var i = 0; i < sorted.length; i++) {
                        result.push(Number(sorted[i]));
                    }
                    return result;
                },

                getInitials(name) {
                    if (!name) return '??';
                    const parts = name.trim().split(' ');
                    if (parts.length === 1) return parts[0].substring(0, 2).toUpperCase();
                    return (parts[0][0] + (parts[parts.length - 1][0] || '')).toUpperCase();
                },

                getRandomColor: function() {
                    var colors = ['#FFC300', '#DAF7A6', '#581845', '#3498DB', '#E74C3C', '#9B59B6', '#F39C12', '#27AE60'];
                    return colors[Math.floor(Math.random() * colors.length)];
                },

                generateId: function() {
                    return Date.now().toString(36) + Math.random().toString(36).substring(2);
                },

                addComment() {
                    if (!this.newComment.author.trim() || !this.newComment.text.trim()) return;
                    
                    const comment = {
                        id: this.generateId(),
                        author: this.newComment.author.trim(),
                        text: this.newComment.text.trim(),
                        timestamp: 'Ahora mismo',
                        likes: 0,
                        userLiked: false,
                        avatarColor: this.getRandomColor(),
                        image: this.newComment.image,
                        isCompany: false,
                        showReplyForm: false,
                        replyAuthor: '',
                        replyText: '',
                        replies: []
                    };
                    
                    this.allComments.unshift(comment);
                    this.saveToStorage('authorName', this.newComment.author);
                    this.newComment.text = '';
                    this.newComment.image = null;
                },

                toggleLike(comment) {
                    comment.userLiked = !comment.userLiked;
                    comment.likes += comment.userLiked ? 1 : -1;
                    if (comment.likes < 0) comment.likes = 0;
                },

                toggleReplyForm(comment) {
                    comment.showReplyForm = !comment.showReplyForm;
                    if (!comment.showReplyForm) {
                        comment.replyAuthor = '';
                        comment.replyText = '';
                    }
                },

                addReply(parentComment) {
                    if (!parentComment.replyAuthor.trim() || !parentComment.replyText.trim()) return;
                    
                    const reply = {
                        id: this.generateId(),
                        author: parentComment.replyAuthor.trim(),
                        text: parentComment.replyText.trim(),
                        timestamp: 'Ahora mismo',
                        likes: 0,
                        userLiked: false,
                        avatarColor: this.getRandomColor(),
                        image: null,
                        isCompany: false,
                        showReplyForm: false,
                        replyAuthor: '',
                        replyText: '',
                        replies: []
                    };
                    
                    if (!parentComment.replies) parentComment.replies = [];
                    parentComment.replies.push(reply);
                    parentComment.showReplyForm = false;
                    parentComment.replyAuthor = '';
                    parentComment.replyText = '';
                },

                getDepth(comment) {
                    // Простая проверка глубины по количеству replies в структуре
                    return 0; // Для простоты, можно улучшить
                },

                handleImageUpload: function(event) {
                    var file = event.target.files[0];
                    if (file && file.type.indexOf('image/') === 0) {
                        var self = this;
                        var reader = new FileReader();
                        reader.onload = function(e) {
                            self.newComment.image = e.target.result;
                        };
                        reader.readAsDataURL(file);
                    }
                },

                removeImage: function() {
                    this.newComment.image = null;
                },

                // Touch события для мобильных устройств
                handleStarTouch: function(star, event) {
                    // Визуальная обратная связь при touch
                    event.target.style.transform = 'scale(1.1)';
                    var self = this;
                    setTimeout(function() {
                        event.target.style.transform = 'scale(1)';
                    }, 150);
                },

                handleButtonTouch: function(event) {
                    // Визуальная обратная связь для кнопок
                    event.target.style.opacity = '0.7';
                    var self = this;
                    setTimeout(function() {
                        event.target.style.opacity = '1';
                    }, 150);
                },

                saveToStorage: function(key, value) {
                    try {
                        // Пробуем sessionStorage
                        if (typeof sessionStorage !== 'undefined') {
                            sessionStorage.setItem(key, JSON.stringify(value));
                            return;
                        }
                    } catch (e) {
                        // Игнорируем ошибку
                    }

                    try {
                        // Fallback на cookies для WebView
                        var expires = '';
                        var date = new Date();
                        date.setTime(date.getTime() + (1 * 24 * 60 * 60 * 1000)); // 1 день
                        expires = '; expires=' + date.toUTCString();
                        document.cookie = 'fb_' + key + '=' + JSON.stringify(value) + expires + '; path=/';
                    } catch (e2) {
                        console.warn('Storage not available');
                    }
                },

                getFromStorage: function(key) {
                    try {
                        // Пробуем sessionStorage
                        if (typeof sessionStorage !== 'undefined') {
                            var stored = sessionStorage.getItem(key);
                            return stored ? JSON.parse(stored) : null;
                        }
                    } catch (e) {
                        // Игнорируем ошибку
                    }

                    try {
                        // Fallback на cookies
                        var nameEQ = 'fb_' + key + '=';
                        var ca = document.cookie.split(';');
                        for (var i = 0; i < ca.length; i++) {
                            var c = ca[i];
                            while (c.charAt(0) === ' ') c = c.substring(1, c.length);
                            if (c.indexOf(nameEQ) === 0) {
                                return JSON.parse(c.substring(nameEQ.length, c.length));
                            }
                        }
                    } catch (e2) {
                        // Игнорируем ошибку
                    }

                    return null;
                }
            }
        }
    </script>

</body>
</html>
